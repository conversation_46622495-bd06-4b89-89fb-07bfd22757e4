'use client';

import Header from '../layout/Header';
import Footer from '../layout/Footer';
import Carousel from '../ui/Carousel';
import Button from '../ui/Button';
import Image from 'next/image';

interface ProductPageProps {
  title: string;
  subtitle: string;
  description: string;
  heroImage: string;
  features: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
  brands: Array<{
    id: string;
    src: string;
    alt: string;
  }>;
  ctaText?: string;
  ctaLink?: string;
  metadata?: {
    title: string;
    description: string;
  };
}

const ProductPageTemplate = ({
  title,
  subtitle,
  description,
  heroImage,
  features,
  brands,
  ctaText = "Prenota Controllo Vista",
  ctaLink = "/esami-vista"
}: ProductPageProps) => {
  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-accent text-lg font-sans font-medium mb-4">
                  {subtitle}
                </p>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
                  {title}
                </h1>
                <p className="text-xl text-text-base opacity-80 leading-relaxed mb-8">
                  {description}
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => window.location.href = ctaLink}
                  >
                    {ctaText}
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = '/contatti'}
                  >
                    Contattaci
                  </Button>
                </div>
              </div>
              <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src={heroImage}
                  alt={title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                Perché Scegliere i Nostri Prodotti
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Qualità, innovazione e attenzione al dettaglio per garantirti sempre il meglio
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center p-6 rounded-lg bg-background hover:shadow-lg transition-shadow duration-300">
                  {feature.icon && (
                    <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-2xl">{feature.icon}</span>
                    </div>
                  )}
                  <h3 className="text-xl font-semibold font-sans text-text-base mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-text-base opacity-80 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Brands Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                I Migliori Marchi
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Collaboriamo con i brand più prestigiosi per offrirti sempre prodotti di eccellenza
              </p>
            </div>
            
            {brands.length > 0 && (
              <div className="max-w-6xl mx-auto">
                <Carousel
                  items={brands}
                  autoPlay={true}
                  autoPlayInterval={3500}
                  showDots={false}
                  showArrows={true}
                  itemsPerView={4}
                  className="brands-carousel"
                />
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
              Prenota una Consulenza
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              I nostri esperti sono a tua disposizione per consigliarti la soluzione migliore 
              per le tue esigenze visive.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/esami-vista'}
              >
                Prenota Controllo Vista
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
                className="border-white text-white hover:bg-white hover:text-primary"
              >
                Vieni a Trovarci
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default ProductPageTemplate;
