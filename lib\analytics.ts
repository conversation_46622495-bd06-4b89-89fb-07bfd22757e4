// Google Analytics 4 configuration and utilities
// Integrates with GDPR compliance and cookie consent

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '';

// Initialize Google Analytics with consent mode
export const initGA = () => {
  if (typeof window === 'undefined' || !GA_MEASUREMENT_ID) return;

  // Initialize dataLayer
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag(...args: any[]) {
    window.dataLayer.push(args);
  };

  // Set default consent state (denied by default for GDPR compliance)
  window.gtag('consent', 'default', {
    analytics_storage: 'denied',
    ad_storage: 'denied',
    ad_user_data: 'denied',
    ad_personalization: 'denied',
    wait_for_update: 500,
  });

  // Configure GA4
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });
};

// Update consent based on user preferences
export const updateConsent = (analytics: boolean, marketing: boolean) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('consent', 'update', {
    analytics_storage: analytics ? 'granted' : 'denied',
    ad_storage: marketing ? 'granted' : 'denied',
    ad_user_data: marketing ? 'granted' : 'denied',
    ad_personalization: marketing ? 'granted' : 'denied',
  });
};

// Track page views
export const trackPageView = (url: string, title?: string) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('config', GA_MEASUREMENT_ID, {
    page_path: url,
    page_title: title || document.title,
  });
};

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number,
  customParameters?: Record<string, any>
) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    ...customParameters,
  });
};

// Predefined event tracking functions
export const trackButtonClick = (buttonName: string, location: string) => {
  trackEvent('click', 'button', `${buttonName} - ${location}`);
};

export const trackLinkClick = (linkText: string, url: string, location: string) => {
  trackEvent('click', 'link', `${linkText} - ${location}`, undefined, {
    link_url: url,
  });
};

export const trackFormSubmission = (formName: string, success: boolean) => {
  trackEvent('form_submit', 'form', formName, undefined, {
    success: success,
  });
};

export const trackFileDownload = (fileName: string, fileType: string) => {
  trackEvent('file_download', 'download', fileName, undefined, {
    file_type: fileType,
  });
};

export const trackVideoInteraction = (action: string, videoTitle: string, progress?: number) => {
  trackEvent(action, 'video', videoTitle, progress);
};

export const trackScrollDepth = (percentage: number) => {
  trackEvent('scroll', 'engagement', `${percentage}%`, percentage);
};

export const trackSearch = (searchTerm: string, resultsCount?: number) => {
  trackEvent('search', 'engagement', searchTerm, resultsCount);
};

export const trackPhoneCall = (phoneNumber: string, location: string) => {
  trackEvent('phone_call', 'contact', `${phoneNumber} - ${location}`);
};

export const trackWhatsAppClick = (location: string) => {
  trackEvent('whatsapp_click', 'contact', location);
};

export const trackEmailClick = (location: string) => {
  trackEvent('email_click', 'contact', location);
};

// Enhanced ecommerce tracking (for future use)
export const trackPurchase = (transactionId: string, value: number, currency: string = 'EUR') => {
  trackEvent('purchase', 'ecommerce', transactionId, value, {
    transaction_id: transactionId,
    currency: currency,
  });
};

// Track user engagement
export const trackEngagement = (engagementTime: number) => {
  trackEvent('user_engagement', 'engagement', 'time_on_page', engagementTime);
};

// Track custom conversions
export const trackConversion = (conversionName: string, value?: number) => {
  trackEvent('conversion', 'goal', conversionName, value);
};

// Scroll depth tracking utility
export const initScrollTracking = () => {
  if (typeof window === 'undefined') return;

  let scrollDepthMarks = [25, 50, 75, 90, 100];
  let scrollDepthReached: number[] = [];

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = Math.round((scrollTop / docHeight) * 100);

    scrollDepthMarks.forEach(mark => {
      if (scrollPercent >= mark && !scrollDepthReached.includes(mark)) {
        scrollDepthReached.push(mark);
        trackScrollDepth(mark);
      }
    });
  };

  window.addEventListener('scroll', handleScroll, { passive: true });

  // Cleanup function
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
};

// Enhanced click tracking for buttons and links
export const initClickTracking = () => {
  if (typeof window === 'undefined') return;

  const handleClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    
    // Track button clicks
    if (target.tagName === 'BUTTON' || target.closest('button')) {
      const button = target.tagName === 'BUTTON' ? target : target.closest('button');
      const buttonText = button?.textContent?.trim() || 'Unknown Button';
      const location = getElementLocation(button);
      trackButtonClick(buttonText, location);
    }
    
    // Track link clicks
    if (target.tagName === 'A' || target.closest('a')) {
      const link = target.tagName === 'A' ? target as HTMLAnchorElement : target.closest('a');
      const linkText = link?.textContent?.trim() || 'Unknown Link';
      const url = link?.href || '';
      const location = getElementLocation(link);
      
      // Special tracking for external links
      if (url && !url.includes(window.location.hostname)) {
        trackEvent('click', 'external_link', `${linkText} - ${url}`, undefined, {
          link_url: url,
          location: location,
        });
      } else {
        trackLinkClick(linkText, url, location);
      }
    }
  };

  document.addEventListener('click', handleClick);

  // Cleanup function
  return () => {
    document.removeEventListener('click', handleClick);
  };
};

// Utility function to get element location context
const getElementLocation = (element: Element | null): string => {
  if (!element) return 'unknown';
  
  // Check for common parent containers
  const section = element.closest('section');
  const header = element.closest('header');
  const footer = element.closest('footer');
  const nav = element.closest('nav');
  
  if (header) return 'header';
  if (footer) return 'footer';
  if (nav) return 'navigation';
  if (section) {
    // Try to get section identifier
    const sectionClass = section.className;
    const sectionId = section.id;
    if (sectionId) return sectionId;
    if (sectionClass) return sectionClass.split(' ')[0] || 'section';
    return 'section';
  }
  
  return 'page';
};

// Performance tracking
export const trackPageLoadTime = () => {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        trackEvent('page_load_time', 'performance', 'load_time', Math.round(loadTime));
      }
    }, 0);
  });
};
