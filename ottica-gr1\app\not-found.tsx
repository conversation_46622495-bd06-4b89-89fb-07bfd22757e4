'use client';

import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Button from '../components/ui/Button';



export default function NotFound() {
  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background min-h-[80vh] flex items-center">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="text-8xl font-bold text-primary mb-8">404</div>
              <h1 className="text-4xl md:text-5xl font-bold font-sans text-text-base mb-6">
                Pagina Non Trovata
              </h1>
              <p className="text-xl text-text-base opacity-80 mb-8 leading-relaxed">
                Ci dispiace, la pagina che stai cercando non esiste o è stata spostata.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => window.location.href = '/'}
                >
                  Torna alla Homepage
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => window.location.href = '/contatti'}
                >
                  Contattaci
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
