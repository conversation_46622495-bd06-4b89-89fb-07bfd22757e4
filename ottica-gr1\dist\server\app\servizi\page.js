(()=>{var e={};e.id=331,e.ids=[331],e.modules={273:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var a=i(7413),s=i(4536),o=i.n(s);let r=()=>{let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,a.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,a.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,a.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,a.jsx)("p",{children:"✉️ <EMAIL>"}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,a.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(o(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,a.jsx)(o(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},418:(e,t,i)=>{"use strict";function a({title:e,description:t,keywords:i="",canonical:a,ogImage:s="/images/og-default.jpg",ogType:o="website",twitterCard:r="summary_large_image",noindex:n=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=a?`${l}${a}`:void 0,p=s.startsWith("http")?s:`${l}${s}`;return{title:c,description:t,keywords:i||void 0,robots:n?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:p,width:1200,height:630,alt:e}],locale:"it_IT",type:o},twitter:{card:r,title:c,description:t,images:[p],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>s,Y:()=>a});let s=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2029:(e,t,i)=>{Promise.resolve().then(i.bind(i,6616)),Promise.resolve().then(i.bind(i,5271)),Promise.resolve().then(i.t.bind(i,4536,23))},2055:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l,metadata:()=>n});var a=i(7413),s=i(5271),o=i(273),r=i(6616);let n=(0,i(418).Y)({title:"I Nostri Servizi - Esami Vista e Servizi Ottici Specializzati",description:"Servizi ottici completi a Montesacro: misurazione optometrica, tonometria, pachimetria, cheratometria, Amplifon Point, foto tessera e laboratorio riparazioni.",keywords:"servizi ottici roma, misurazione optometrica, tonometria, pachimetria, cheratometria, amplifon point, foto tessera, riparazione occhiali montesacro",canonical:"/servizi"});function l(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsx)(r.default,{})}),(0,a.jsx)(o.A,{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4536:(e,t,i)=>{let{createProxy:a}=i(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5271:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},5645:(e,t,i)=>{Promise.resolve().then(i.bind(i,7546)),Promise.resolve().then(i.bind(i,3121)),Promise.resolve().then(i.t.bind(i,5814,23))},6616:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\servizi\\\\ServiziContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\servizi\\ServiziContent.tsx","default")},7546:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var a=i(687),s=i(474),o=i(2294);function r(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:["I Nostri ",(0,a.jsx)("span",{className:"text-primary",children:"Servizi"})]}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-text-base opacity-80 leading-relaxed",children:"Offriamo una gamma completa di servizi dedicati alla salute e al benessere dei tuoi occhi, unendo l'esperienza quarantennale di Ottica GR1 alle tecnologie pi\xf9 avanzate."})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:[{src:"/images/servizi/DSC09581.jpeg",alt:"Strumentazione professionale per esami della vista"},{src:"/images/servizi/DSC09591.jpeg",alt:"Apparecchiature diagnostiche avanzate"},{src:"/images/servizi/servizio.jpeg",alt:"Laboratorio e strumenti di precisione"},{src:"/images/servizi/DSC09607.jpeg",alt:"Area consulenza e servizi specializzati"}].map((e,t)=>(0,a.jsxs)("div",{className:"relative h-64 rounded-lg overflow-hidden shadow-lg group",children:[(0,a.jsx)(s.default,{src:e.src,alt:e.alt,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"})]},t))})})}),(0,a.jsx)("section",{className:"py-16 bg-background",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Servizi Specialistici"}),(0,a.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Tecnologie avanzate e competenza professionale per la cura della tua vista"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[{title:"Misurazione Optometrica",description:"La misurazione optometrica \xe8 il cuore della nostra consulenza visiva. Attraverso strumenti all'avanguardia e la competenza dei nostri ottici optometristi, determiniamo con precisione le tue diottrie e l'eventuale presenza di difetti visivi come miopia, ipermetropia, astigmatismo o presbiopia. Questo esame approfondito ci permette di prescrivere le lenti pi\xf9 adatte alle tue specifiche esigenze, garantendo una visione nitida e confortevole in ogni situazione.",icon:"\uD83D\uDC41️"},{title:"Tonometria",description:"La tonometria \xe8 un esame rapido e indolore che misura la pressione intraoculare. \xc8 un controllo fondamentale per la prevenzione e il monitoraggio del glaucoma, una patologia che, se non diagnosticata e trattata per tempo, pu\xf2 compromettere seriamente la vista. Grazie a questo servizio, possiamo intercettare precocemente eventuali anomalie e consigliarti i passi successivi per la cura dei tuoi occhi.",icon:"\uD83D\uDCCA"},{title:"Pachimetria",description:"La pachimetria \xe8 una tecnica non invasiva che misura lo spessore della cornea. Questo dato \xe8 cruciale, specialmente in presenza di pressione oculare elevata, poich\xe9 lo spessore corneale pu\xf2 influenzare l'accuratezza delle misurazioni della pressione. \xc8 un esame essenziale per una diagnosi pi\xf9 precisa e per valutare l'idoneit\xe0 a certi trattamenti o interventi.",icon:"\uD83D\uDCCF"},{title:"Cheratometria",description:"La cheratometria misura la curvatura della superficie anteriore della cornea. Questo esame \xe8 indispensabile per l'applicazione delle lenti a contatto, poich\xe9 ci permette di scegliere la lente con la curvatura pi\xf9 adatta al tuo occhio, assicurando comfort e una visione ottimale. \xc8 inoltre utile per la diagnosi e il monitoraggio di patologie corneali come il cheratocono.",icon:"\uD83D\uDCD0"}].map((e,t)=>(0,a.jsx)("div",{className:"bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"text-4xl flex-shrink-0",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold font-sans text-text-base mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed",children:e.description})]})]})},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Servizi Aggiuntivi"}),(0,a.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Oltre alla cura della vista, offriamo anche servizi pratici per le tue esigenze quotidiane"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Amplifon Point",description:"Siamo Amplifon Point, il tuo punto di riferimento per l'udito. Presso il nostro negozio puoi trovare consulenza specializzata, effettuare test gratuiti dell'udito e scoprire le soluzioni acustiche pi\xf9 innovative e adatte a te. Un servizio comodo e professionale per prenderti cura anche del tuo benessere uditivo.",icon:"\uD83D\uDD0A"},{title:"Foto Tessera",description:"Hai bisogno di foto per documenti d'identit\xe0, passaporti, patenti o visti? Offriamo un servizio rapido e professionale di foto tessera, realizzate secondo le normative vigenti per tutti i tipi di documenti. Un modo veloce e comodo per ottenere le tue foto senza stress.",icon:"\uD83D\uDCF7"},{title:"Laboratorio Riparazioni Occhiali",description:"Il nostro laboratorio interno \xe8 specializzato nella riparazione e manutenzione dei tuoi occhiali da vista e da sole. Che si tratti di una montatura rotta, una vite allentata o una lente da sostituire, il nostro team esperto \xe8 pronto a intervenire con precisione e rapidit\xe0 per ridare vita ai tuoi occhiali, garantendoti di non rimanere mai senza il tuo prezioso ausilio visivo.",icon:"\uD83D\uDD27"}].map((e,t)=>(0,a.jsx)("div",{className:"bg-background p-6 rounded-lg hover:shadow-lg transition-shadow duration-300",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,a.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed",children:e.description})]})},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Prenota il Tuo Servizio"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Contattaci per prenotare uno dei nostri servizi specializzati. Il nostro team \xe8 pronto ad offrirti la migliore assistenza professionale."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(o.A,{variant:"accent",size:"lg",onClick:()=>window.location.href="/contatti",children:"Prenota Ora"}),(0,a.jsx)(o.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="tel:068862962",className:"border-white text-white hover:bg-white hover:text-primary",children:"Chiama: 068862962"})]})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9985:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=i(5239),s=i(8088),o=i(8170),r=i.n(o),n=i(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(t,l);let c={children:["",{children:["servizi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,2055)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\servizi\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\servizi\\page.tsx"],p={require:i,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/servizi/page",pathname:"/servizi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,988,926],()=>i(9985));module.exports=a})();