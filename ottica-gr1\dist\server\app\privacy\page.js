/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/privacy/page";
exports.ids = ["app/privacy/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"286d1b8f6c0b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ2lvdmFcXERlc2t0b3BcXFByb2dldHRpbmlcXG90dGljYS1ncjFcXG90dGljYS1ncjFcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyODZkMWI4ZjZjMGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Source_Serif_4_arguments_subsets_latin_variable_font_source_serif_display_swap_variableName_sourceSerif___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Source_Serif_4\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-source-serif\",\"display\":\"swap\"}],\"variableName\":\"sourceSerif\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Source_Serif_4\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-source-serif\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"sourceSerif\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Source_Serif_4_arguments_subsets_latin_variable_font_source_serif_display_swap_variableName_sourceSerif___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Source_Serif_4_arguments_subsets_latin_variable_font_source_serif_display_swap_variableName_sourceSerif___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_CookieConsent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/CookieConsent */ \"(rsc)/./components/ui/CookieConsent.tsx\");\n/* harmony import */ var _components_ui_WhatsAppButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/WhatsAppButton */ \"(rsc)/./components/ui/WhatsAppButton.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Ottica GR1 - Occhiali da Vista, da Sole e Lenti a Contatto\",\n    description: \"Ottica GR1 a Montesacro dal 1982. Occhiali da vista, da sole, lenti a contatto e controllo vista. Tradizione, qualità e innovazione per la tua salute visiva.\",\n    keywords: \"ottica, occhiali da vista, occhiali da sole, lenti a contatto, controllo vista, Montesacro, Roma\",\n    icons: {\n        icon: [\n            {\n                url: '/favicon-16x16.png',\n                sizes: '16x16',\n                type: 'image/png'\n            },\n            {\n                url: '/favicon-32x32.png',\n                sizes: '32x32',\n                type: 'image/png'\n            },\n            {\n                url: '/favicon.ico',\n                sizes: 'any'\n            }\n        ],\n        apple: [\n            {\n                url: '/apple-touch-icon.png',\n                sizes: '180x180',\n                type: 'image/png'\n            }\n        ],\n        other: [\n            {\n                rel: 'android-chrome-192x192',\n                url: '/android-chrome-192x192.png'\n            },\n            {\n                rel: 'android-chrome-512x512',\n                url: '/android-chrome-512x512.png'\n            }\n        ]\n    },\n    manifest: '/site.webmanifest'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"it\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Source_Serif_4_arguments_subsets_latin_variable_font_source_serif_display_swap_variableName_sourceSerif___WEBPACK_IMPORTED_MODULE_5___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-serif antialiased\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CookieConsent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_WhatsAppButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"https://cdn.trustindex.io/loader.js\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/privacy/page.tsx":
/*!******************************!*\
  !*** ./app/privacy/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PrivacyPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/layout/Header */ \"(rsc)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n/* harmony import */ var _components_seo_SEOHead__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/seo/SEOHead */ \"(rsc)/./components/seo/SEOHead.tsx\");\n\n\n\n\nconst metadata = (0,_components_seo_SEOHead__WEBPACK_IMPORTED_MODULE_3__.generateSEOMetadata)({\n    title: 'Privacy Policy - Ottica GR1',\n    description: 'Informativa sulla privacy di Ottica GR1. Come raccogliamo, utilizziamo e proteggiamo i tuoi dati personali.',\n    keywords: 'privacy policy, protezione dati, gdpr, ottica gr1, informativa privacy',\n    canonical: '/privacy'\n});\nfunction PrivacyPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold font-sans text-primary mb-8\",\n                                children: \"Privacy Policy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-text-base mb-8\",\n                                        children: [\n                                            \"Ultimo aggiornamento: \",\n                                            new Date().toLocaleDateString('it-IT')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"1. Introduzione\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: 'Ottica GR1 (di seguito \"noi\", \"nostro\" o \"la Societ\\xe0\") rispetta la tua privacy e si impegna a proteggere i tuoi dati personali. Questa informativa sulla privacy ti informa su come trattiamo i tuoi dati personali quando visiti il nostro sito web e ti informa sui tuoi diritti in materia di privacy e su come la legge ti protegge.'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"2. Dati che raccogliamo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: \"Possiamo raccogliere, utilizzare, archiviare e trasferire diversi tipi di dati personali su di te:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 text-text-base mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dati di identit\\xe0: nome, cognome\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dati di contatto: indirizzo email, numero di telefono\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dati tecnici: indirizzo IP, tipo di browser, fuso orario\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dati di utilizzo: informazioni su come utilizzi il nostro sito web\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"3. Come utilizziamo i tuoi dati\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: \"Utilizziamo i tuoi dati personali per:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 text-text-base mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Rispondere alle tue richieste di informazioni\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Prenotare appuntamenti per controlli della vista\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Migliorare il nostro sito web e i nostri servizi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Inviarti comunicazioni di marketing (solo con il tuo consenso)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"4. I tuoi diritti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: \"In base al GDPR, hai i seguenti diritti:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 text-text-base mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto di accesso ai tuoi dati personali\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto di rettifica dei tuoi dati personali\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto di cancellazione dei tuoi dati personali\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto di limitazione del trattamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto alla portabilit\\xe0 dei dati\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Diritto di opposizione al trattamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"5. Sicurezza dei dati\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: \"Abbiamo messo in atto misure di sicurezza appropriate per prevenire la perdita accidentale, l'uso o l'accesso non autorizzato, l'alterazione e la divulgazione dei tuoi dati personali.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold font-sans text-primary mb-4\",\n                                                children: \"6. Contatti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-base mb-4\",\n                                                children: \"Se hai domande su questa informativa sulla privacy o sui tuoi dati personali, contattaci:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-background p-6 rounded-lg border border-border-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-text-base mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Titolare dei dati:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 54\n                                                            }, this),\n                                                            \" Antonio Gualerzi\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-text-base mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 54\n                                                            }, this),\n                                                            \" <EMAIL>\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-text-base mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Telefono:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 54\n                                                            }, this),\n                                                            \"06 8862962 | +39 3928480621\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-text-base\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Indirizzo:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            \" Via Conca D'Oro, 323, 00141 ROMA RM\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/privacy/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-primary text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold font-sans\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white\",\n                                            children: \"OTTICA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-accent ml-1\",\n                                            children: \"GR1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 14,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm leading-relaxed opacity-90\",\n                                    children: \"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Link Rapidi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/storia\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"La Nostra Storia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/servizi\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"I Nostri Servizi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/occhiali\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Occhiali da Vista\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/occhiali-sole\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Occhiali da Sole\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/lenti-contatto\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Lenti a Contatto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Servizi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/esami-vista\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Esami della Vista\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Consulenza Personalizzata\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Riparazioni\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Assistenza Post-Vendita\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Contatti\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCDE 06 8862962 | +39 3928480621 \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✉️ <EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Orari di Apertura:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Lun: 16:00 - 19:30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Dom: Chiuso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-primary-light mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-75\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookie\",\n                                        className: \"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/seo/SEOHead.tsx":
/*!************************************!*\
  !*** ./components/seo/SEOHead.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLocalBusinessStructuredData: () => (/* binding */ generateLocalBusinessStructuredData),\n/* harmony export */   generateSEOMetadata: () => (/* binding */ generateSEOMetadata)\n/* harmony export */ });\nfunction generateSEOMetadata({ title, description, keywords = '', canonical, ogImage = '/images/og-default.jpg', ogType = 'website', twitterCard = 'summary_large_image', noindex = false }) {\n    const baseUrl = 'https://otticagr1.it'; // Replace with actual domain\n    const fullTitle = title.includes('Ottica GR1') ? title : `${title} - Ottica GR1`;\n    const canonicalUrl = canonical ? `${baseUrl}${canonical}` : undefined;\n    const imageUrl = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`;\n    return {\n        title: fullTitle,\n        description,\n        keywords: keywords || undefined,\n        robots: noindex ? 'noindex,nofollow' : 'index,follow',\n        openGraph: {\n            title: fullTitle,\n            description,\n            url: canonicalUrl,\n            siteName: 'Ottica GR1',\n            images: [\n                {\n                    url: imageUrl,\n                    width: 1200,\n                    height: 630,\n                    alt: title\n                }\n            ],\n            locale: 'it_IT',\n            type: ogType\n        },\n        twitter: {\n            card: twitterCard,\n            title: fullTitle,\n            description,\n            images: [\n                imageUrl\n            ],\n            creator: '@otticagr1',\n            site: '@otticagr1'\n        },\n        alternates: {\n            canonical: canonicalUrl\n        },\n        other: {\n            'geo.region': 'IT-RM',\n            'geo.placename': 'Roma',\n            'geo.position': '41.9028;12.4964',\n            'ICBM': '41.9028, 12.4964'\n        }\n    };\n}\n// Structured Data for Local Business\nconst generateLocalBusinessStructuredData = ()=>{\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'LocalBusiness',\n        '@id': 'https://otticagr1.it/#business',\n        name: 'Ottica GR1',\n        description: 'Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.',\n        url: 'https://otticagr1.it',\n        telephone: '+39-**************',\n        email: '<EMAIL>',\n        address: {\n            '@type': 'PostalAddress',\n            streetAddress: 'Via Montesacro, 123',\n            addressLocality: 'Roma',\n            addressRegion: 'Lazio',\n            postalCode: '00141',\n            addressCountry: 'IT'\n        },\n        geo: {\n            '@type': 'GeoCoordinates',\n            latitude: 41.9028,\n            longitude: 12.4964\n        },\n        openingHoursSpecification: [\n            {\n                '@type': 'OpeningHoursSpecification',\n                dayOfWeek: [\n                    'Monday',\n                    'Tuesday',\n                    'Wednesday',\n                    'Thursday',\n                    'Friday'\n                ],\n                opens: '09:00',\n                closes: '19:30'\n            },\n            {\n                '@type': 'OpeningHoursSpecification',\n                dayOfWeek: 'Saturday',\n                opens: '09:00',\n                closes: '13:00'\n            }\n        ],\n        priceRange: '€€',\n        image: 'https://otticagr1.it/images/og-default.jpg',\n        logo: 'https://otticagr1.it/images/logo.png',\n        sameAs: [\n            'https://www.facebook.com/otticagr1',\n            'https://www.instagram.com/otticagr1'\n        ],\n        hasOfferCatalog: {\n            '@type': 'OfferCatalog',\n            name: 'Servizi Ottici',\n            itemListElement: [\n                {\n                    '@type': 'Offer',\n                    itemOffered: {\n                        '@type': 'Service',\n                        name: 'Occhiali da Vista',\n                        description: 'Vendita e consulenza per occhiali da vista personalizzati'\n                    }\n                },\n                {\n                    '@type': 'Offer',\n                    itemOffered: {\n                        '@type': 'Service',\n                        name: 'Occhiali da Sole',\n                        description: 'Ampia selezione di occhiali da sole di marca'\n                    }\n                },\n                {\n                    '@type': 'Offer',\n                    itemOffered: {\n                        '@type': 'Service',\n                        name: 'Lenti a Contatto',\n                        description: 'Lenti a contatto di tutte le tipologie'\n                    }\n                },\n                {\n                    '@type': 'Offer',\n                    itemOffered: {\n                        '@type': 'Service',\n                        name: 'Controllo Vista',\n                        description: 'Esami della vista professionali con strumentazione avanzata'\n                    }\n                }\n            ]\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/seo/SEOHead.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/CookieConsent.tsx":
/*!*****************************************!*\
  !*** ./components/ui/CookieConsent.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\ui\\CookieConsent.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/ui/WhatsAppButton.tsx":
/*!******************************************!*\
  !*** ./components/ui/WhatsAppButton.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\ui\\WhatsAppButton.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprivacy%2Fpage&page=%2Fprivacy%2Fpage&appPaths=%2Fprivacy%2Fpage&pagePath=private-next-app-dir%2Fprivacy%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprivacy%2Fpage&page=%2Fprivacy%2Fpage&appPaths=%2Fprivacy%2Fpage&pagePath=private-next-app-dir%2Fprivacy%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/privacy/page.tsx */ \"(rsc)/./app/privacy/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'privacy',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\privacy\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/privacy/page\",\n        pathname: \"/privacy\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprivacy%2Fpage&page=%2Fprivacy%2Fpage&appPaths=%2Fprivacy%2Fpage&pagePath=private-next-app-dir%2Fprivacy%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/CookieConsent.tsx */ \"(rsc)/./components/ui/CookieConsent.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/WhatsAppButton.tsx */ \"(rsc)/./components/ui/WhatsAppButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dpb3ZhJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpbmklNUMlNUNvdHRpY2EtZ3IxJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q2FwcCU1QyU1Q25vdC1mb3VuZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ2lvdmFcXFxcRGVza3RvcFxcXFxQcm9nZXR0aW5pXFxcXG90dGljYS1ncjFcXFxcb3R0aWNhLWdyMVxcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(rsc)/./components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dpb3ZhJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpbmklNUMlNUNvdHRpY2EtZ3IxJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNIZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNnaW92YSU1QyU1Q0Rlc2t0b3AlNUMlNUNQcm9nZXR0aW5pJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q290dGljYS1ncjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2FwcC1kaXIlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUErSjtBQUMvSjtBQUNBLGdOQUFnTSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGdpb3ZhXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGluaVxcXFxvdHRpY2EtZ3IxXFxcXG90dGljYS1ncjFcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGdpb3ZhXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGluaVxcXFxvdHRpY2EtZ3IxXFxcXG90dGljYS1ncjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/layout/Header */ \"(ssr)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/layout/Footer */ \"(ssr)/./components/layout/Footer.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-16 bg-gradient-to-b from-primary/5 to-background min-h-[80vh] flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-8xl font-bold text-primary mb-8\",\n                                    children: \"404\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold font-sans text-text-base mb-6\",\n                                    children: \"Pagina Non Trovata\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-text-base opacity-80 mb-8 leading-relaxed\",\n                                    children: \"Ci dispiace, la pagina che stai cercando non esiste o \\xe8 stata spostata.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            variant: \"primary\",\n                                            size: \"lg\",\n                                            onClick: ()=>window.location.href = '/',\n                                            children: \"Torna alla Homepage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            onClick: ()=>window.location.href = '/contatti',\n                                            children: \"Contattaci\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-primary text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold font-sans\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white\",\n                                            children: \"OTTICA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-accent ml-1\",\n                                            children: \"GR1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 14,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm leading-relaxed opacity-90\",\n                                    children: \"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Link Rapidi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/storia\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"La Nostra Storia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/servizi\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"I Nostri Servizi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/occhiali\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Occhiali da Vista\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/occhiali-sole\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Occhiali da Sole\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/lenti-contatto\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Lenti a Contatto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Servizi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/esami-vista\",\n                                                className: \"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                                children: \"Esami della Vista\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Consulenza Personalizzata\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Riparazioni\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Assistenza Post-Vendita\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold font-sans\",\n                                    children: \"Contatti\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCDE 06 8862962 | +39 3928480621 \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✉️ <EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Orari di Apertura:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Lun: 16:00 - 19:30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Dom: Chiuso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-primary-light mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-75\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookie\",\n                                        className: \"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const isHomepage = pathname === '/';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    // Prevent body scroll when mobile menu is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (isMobileMenuOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            // Cleanup on unmount\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], [\n        isMobileMenuOpen\n    ]);\n    const navLinks = [\n        {\n            href: '/',\n            label: 'HOME'\n        },\n        {\n            href: '/storia',\n            label: 'STORIA'\n        },\n        {\n            href: '/servizi',\n            label: 'SERVIZI'\n        },\n        {\n            href: '/occhiali',\n            label: 'OCCHIALI DA VISTA'\n        },\n        {\n            href: '/lenti-contatto',\n            label: 'LENTI A CONTATTO'\n        },\n        {\n            href: '/occhiali-sole',\n            label: 'OCCHIALI DA SOLE'\n        },\n        {\n            href: '/esami-vista',\n            label: 'ESAMI DELLA VISTA'\n        },\n        {\n            href: '/contatti',\n            label: 'CONTATTI'\n        }\n    ];\n    // Determine header background based on page and scroll state\n    const getHeaderBackground = ()=>{\n        if (isScrolled) {\n            return 'bg-white shadow-lg';\n        }\n        if (isHomepage) {\n            return 'bg-white shadow-sm';\n        }\n        // Disable backdrop-blur when mobile menu is open to prevent interference\n        if (isMobileMenuOpen) {\n            return 'bg-white shadow-sm';\n        }\n        return 'bg-white/90 shadow-sm';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${getHeaderBackground()}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto px-4 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-10 w-10 flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/images/logo/logo3.png\",\n                                        alt: \"Ottica GR1 Logo\",\n                                        fill: true,\n                                        className: \"object-contain\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold font-sans\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary transition-colors duration-300\",\n                                            children: \"OTTICA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-accent transition-colors duration-300 ml-1\",\n                                            children: \"GR1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: link.href,\n                                    className: `font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ${pathname === link.href ? 'text-primary' : 'text-text-base hover:text-primary'}`,\n                                    children: link.label\n                                }, link.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"lg:hidden flex flex-col space-y-1 w-6 h-6\",\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            \"aria-label\": \"Toggle mobile menu\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? 'opacity-0' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]\",\n                    onClick: ()=>setIsMobileMenuOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`,\n                    style: {\n                        backgroundColor: '#ffffff',\n                        backdropFilter: 'none'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-8 w-8 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/images/logo/logo3.png\",\n                                                    alt: \"Ottica GR1 Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold font-sans\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-primary\",\n                                                        children: \"OTTICA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-accent ml-1\",\n                                                        children: \"GR1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                        \"aria-label\": \"Close menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 px-6 py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: link.href,\n                                            className: `block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ${pathname === link.href ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1'}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: link.label\n                                        }, link.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: \"Dal 1982 a Montesacro\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"Tradizione e innovazione\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/Button.tsx":
/*!**********************************!*\
  !*** ./components/ui/Button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst Button = ({ children, variant = 'primary', size = 'md', fullWidth = false, className = '', ...props })=>{\n    const baseClasses = 'font-sans font-medium transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'bg-primary text-white hover:bg-primary-dark focus:ring-primary',\n        secondary: 'bg-gray-200 text-text-base hover:bg-gray-300 focus:ring-gray-400',\n        accent: 'bg-accent text-white hover:bg-accent-light focus:ring-accent',\n        outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary'\n    };\n    const sizeClasses = {\n        sm: 'px-3 py-1.5 text-sm',\n        md: 'px-6 py-2.5 text-base',\n        lg: 'px-8 py-3 text-lg'\n    };\n    const widthClass = fullWidth ? 'w-full' : '';\n    const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`.trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: combinedClasses,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL0J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQVdBLE1BQU1BLFNBQVMsQ0FBQyxFQUNkQyxRQUFRLEVBQ1JDLFVBQVUsU0FBUyxFQUNuQkMsT0FBTyxJQUFJLEVBQ1hDLFlBQVksS0FBSyxFQUNqQkMsWUFBWSxFQUFFLEVBQ2QsR0FBR0MsT0FDUztJQUNaLE1BQU1DLGNBQWM7SUFFcEIsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxTQUFTO0lBQ1g7SUFFQSxNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsYUFBYWIsWUFBWSxXQUFXO0lBRTFDLE1BQU1jLGtCQUFrQixHQUFHWCxZQUFZLENBQUMsRUFBRUMsY0FBYyxDQUFDTixRQUFRLENBQUMsQ0FBQyxFQUFFVyxXQUFXLENBQUNWLEtBQUssQ0FBQyxDQUFDLEVBQUVjLFdBQVcsQ0FBQyxFQUFFWixXQUFXLENBQUNjLElBQUk7SUFFeEgscUJBQ0UsOERBQUNDO1FBQU9mLFdBQVdhO1FBQWtCLEdBQUdaLEtBQUs7a0JBQzFDTDs7Ozs7O0FBR1A7QUFFQSxpRUFBZUQsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnaW92YVxcRGVza3RvcFxcUHJvZ2V0dGluaVxcb3R0aWNhLWdyMVxcb3R0aWNhLWdyMVxcY29tcG9uZW50c1xcdWlcXEJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgQnV0dG9uSFRNTEF0dHJpYnV0ZXMsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBCdXR0b25Qcm9wcyBleHRlbmRzIEJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxuICB2YXJpYW50PzogJ3ByaW1hcnknIHwgJ3NlY29uZGFyeScgfCAnYWNjZW50JyB8ICdvdXRsaW5lJztcclxuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xyXG4gIGZ1bGxXaWR0aD86IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IEJ1dHRvbiA9ICh7XHJcbiAgY2hpbGRyZW4sXHJcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcclxuICBzaXplID0gJ21kJyxcclxuICBmdWxsV2lkdGggPSBmYWxzZSxcclxuICBjbGFzc05hbWUgPSAnJyxcclxuICAuLi5wcm9wc1xyXG59OiBCdXR0b25Qcm9wcykgPT4ge1xyXG4gIGNvbnN0IGJhc2VDbGFzc2VzID0gJ2ZvbnQtc2FucyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQnO1xyXG4gIFxyXG4gIGNvbnN0IHZhcmlhbnRDbGFzc2VzID0ge1xyXG4gICAgcHJpbWFyeTogJ2JnLXByaW1hcnkgdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LWRhcmsgZm9jdXM6cmluZy1wcmltYXJ5JyxcclxuICAgIHNlY29uZGFyeTogJ2JnLWdyYXktMjAwIHRleHQtdGV4dC1iYXNlIGhvdmVyOmJnLWdyYXktMzAwIGZvY3VzOnJpbmctZ3JheS00MDAnLFxyXG4gICAgYWNjZW50OiAnYmctYWNjZW50IHRleHQtd2hpdGUgaG92ZXI6YmctYWNjZW50LWxpZ2h0IGZvY3VzOnJpbmctYWNjZW50JyxcclxuICAgIG91dGxpbmU6ICdib3JkZXItMiBib3JkZXItcHJpbWFyeSB0ZXh0LXByaW1hcnkgaG92ZXI6YmctcHJpbWFyeSBob3Zlcjp0ZXh0LXdoaXRlIGZvY3VzOnJpbmctcHJpbWFyeScsXHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XHJcbiAgICBzbTogJ3B4LTMgcHktMS41IHRleHQtc20nLFxyXG4gICAgbWQ6ICdweC02IHB5LTIuNSB0ZXh0LWJhc2UnLFxyXG4gICAgbGc6ICdweC04IHB5LTMgdGV4dC1sZycsXHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgd2lkdGhDbGFzcyA9IGZ1bGxXaWR0aCA/ICd3LWZ1bGwnIDogJyc7XHJcblxyXG4gIGNvbnN0IGNvbWJpbmVkQ2xhc3NlcyA9IGAke2Jhc2VDbGFzc2VzfSAke3ZhcmlhbnRDbGFzc2VzW3ZhcmlhbnRdfSAke3NpemVDbGFzc2VzW3NpemVdfSAke3dpZHRoQ2xhc3N9ICR7Y2xhc3NOYW1lfWAudHJpbSgpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGJ1dHRvbiBjbGFzc05hbWU9e2NvbWJpbmVkQ2xhc3Nlc30gey4uLnByb3BzfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9idXR0b24+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEJ1dHRvbjtcclxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsImNoaWxkcmVuIiwidmFyaWFudCIsInNpemUiLCJmdWxsV2lkdGgiLCJjbGFzc05hbWUiLCJwcm9wcyIsImJhc2VDbGFzc2VzIiwidmFyaWFudENsYXNzZXMiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwiYWNjZW50Iiwib3V0bGluZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwid2lkdGhDbGFzcyIsImNvbWJpbmVkQ2xhc3NlcyIsInRyaW0iLCJidXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/CookieConsent.tsx":
/*!*****************************************!*\
  !*** ./components/ui/CookieConsent.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CookieConsent = ()=>{\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreferences, setShowPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        necessary: true,\n        analytics: false,\n        marketing: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieConsent.useEffect\": ()=>{\n            // Check if user has already made a choice\n            const consent = localStorage.getItem('ottica-gr1-cookie-consent');\n            if (!consent) {\n                setShowBanner(true);\n            } else {\n                const savedPreferences = JSON.parse(consent);\n                setPreferences(savedPreferences);\n                // Initialize Google Consent Mode based on saved preferences\n                initializeGoogleConsent(savedPreferences);\n            }\n        }\n    }[\"CookieConsent.useEffect\"], []);\n    const initializeGoogleConsent = (prefs)=>{\n        // Google Consent Mode v2 implementation\n        if (false) {}\n    };\n    const handleAcceptAll = ()=>{\n        const newPreferences = {\n            necessary: true,\n            analytics: true,\n            marketing: true\n        };\n        savePreferences(newPreferences);\n    };\n    const handleRejectAll = ()=>{\n        const newPreferences = {\n            necessary: true,\n            analytics: false,\n            marketing: false\n        };\n        savePreferences(newPreferences);\n    };\n    const handleSavePreferences = ()=>{\n        savePreferences(preferences);\n        setShowPreferences(false);\n    };\n    const savePreferences = (prefs)=>{\n        localStorage.setItem('ottica-gr1-cookie-consent', JSON.stringify(prefs));\n        setPreferences(prefs);\n        setShowBanner(false);\n        initializeGoogleConsent(prefs);\n    };\n    const handlePreferenceChange = (type)=>{\n        if (type === 'necessary') return; // Cannot disable necessary cookies\n        setPreferences((prev)=>({\n                ...prev,\n                [type]: !prev[type]\n            }));\n    };\n    if (!showBanner) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-border-gray shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold font-sans text-text-base mb-2\",\n                                        children: \"Utilizziamo i Cookie\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-base opacity-80 leading-relaxed\",\n                                        children: \"Utilizziamo cookie necessari per il funzionamento del sito e cookie opzionali per analisi e marketing. Puoi scegliere quali accettare o rifiutare tutti i cookie opzionali.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 min-w-fit\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowPreferences(true),\n                                        className: \"text-sm\",\n                                        children: \"Personalizza\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleRejectAll,\n                                        className: \"text-sm\",\n                                        children: \"Rifiuta Tutto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"primary\",\n                                        size: \"sm\",\n                                        onClick: handleAcceptAll,\n                                        className: \"text-sm\",\n                                        children: \"Accetta Tutto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            showPreferences && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-60 bg-black bg-opacity-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold font-sans text-text-base mb-4\",\n                                children: \"Preferenze Cookie\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-text-base opacity-80 mb-6\",\n                                children: \"Gestisci le tue preferenze sui cookie. I cookie necessari sono sempre attivi per garantire il corretto funzionamento del sito.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-border-gray rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold font-sans text-text-base\",\n                                                        children: \"Cookie Necessari\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium\",\n                                                        children: \"Sempre Attivi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-base opacity-80\",\n                                                children: \"Questi cookie sono essenziali per il funzionamento del sito web e non possono essere disabilitati.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-border-gray rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold font-sans text-text-base\",\n                                                        children: \"Cookie Analitici\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences.analytics,\n                                                                onChange: ()=>handlePreferenceChange('analytics'),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-base opacity-80\",\n                                                children: \"Ci aiutano a capire come i visitatori interagiscono con il sito raccogliendo informazioni anonime.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-border-gray rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold font-sans text-text-base\",\n                                                        children: \"Cookie Marketing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences.marketing,\n                                                                onChange: ()=>handlePreferenceChange('marketing'),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-base opacity-80\",\n                                                children: \"Utilizzati per mostrare annunci pubblicitari pi\\xf9 rilevanti per te e i tuoi interessi.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowPreferences(false),\n                                        className: \"flex-1\",\n                                        children: \"Annulla\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"primary\",\n                                        onClick: handleSavePreferences,\n                                        className: \"flex-1\",\n                                        children: \"Salva Preferenze\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL0Nvb2tpZUNvbnNlbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFNEM7QUFDZDtBQVE5QixNQUFNRyxnQkFBZ0I7SUFDcEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdMLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ00saUJBQWlCQyxtQkFBbUIsR0FBR1AsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFvQjtRQUNoRVUsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLFdBQVc7SUFDYjtJQUVBWCxnREFBU0E7bUNBQUM7WUFDUiwwQ0FBMEM7WUFDMUMsTUFBTVksVUFBVUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3JDLElBQUksQ0FBQ0YsU0FBUztnQkFDWlIsY0FBYztZQUNoQixPQUFPO2dCQUNMLE1BQU1XLG1CQUFtQkMsS0FBS0MsS0FBSyxDQUFDTDtnQkFDcENKLGVBQWVPO2dCQUNmLDREQUE0RDtnQkFDNURHLHdCQUF3Qkg7WUFDMUI7UUFDRjtrQ0FBRyxFQUFFO0lBRUwsTUFBTUcsMEJBQTBCLENBQUNDO1FBQy9CLHdDQUF3QztRQUN4QyxJQUFJLEtBQXFELEVBQUUsRUFPMUQ7SUFDSDtJQUVBLE1BQU1PLGtCQUFrQjtRQUN0QixNQUFNQyxpQkFBaUI7WUFDckJsQixXQUFXO1lBQ1hDLFdBQVc7WUFDWEMsV0FBVztRQUNiO1FBQ0FpQixnQkFBZ0JEO0lBQ2xCO0lBRUEsTUFBTUUsa0JBQWtCO1FBQ3RCLE1BQU1GLGlCQUFpQjtZQUNyQmxCLFdBQVc7WUFDWEMsV0FBVztZQUNYQyxXQUFXO1FBQ2I7UUFDQWlCLGdCQUFnQkQ7SUFDbEI7SUFFQSxNQUFNRyx3QkFBd0I7UUFDNUJGLGdCQUFnQnJCO1FBQ2hCRCxtQkFBbUI7SUFDckI7SUFFQSxNQUFNc0Isa0JBQWtCLENBQUNUO1FBQ3ZCTixhQUFha0IsT0FBTyxDQUFDLDZCQUE2QmYsS0FBS2dCLFNBQVMsQ0FBQ2I7UUFDakVYLGVBQWVXO1FBQ2ZmLGNBQWM7UUFDZGMsd0JBQXdCQztJQUMxQjtJQUVBLE1BQU1jLHlCQUF5QixDQUFDQztRQUM5QixJQUFJQSxTQUFTLGFBQWEsUUFBUSxtQ0FBbUM7UUFDckUxQixlQUFlMkIsQ0FBQUEsT0FBUztnQkFDdEIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRCxLQUFLLEVBQUUsQ0FBQ0MsSUFBSSxDQUFDRCxLQUFLO1lBQ3JCO0lBQ0Y7SUFFQSxJQUFJLENBQUMvQixZQUFZLE9BQU87SUFFeEIscUJBQ0U7OzBCQUVFLDhEQUFDaUM7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUdELFdBQVU7a0RBQXNEOzs7Ozs7a0RBR3BFLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBb0Q7Ozs7Ozs7Ozs7OzswQ0FLbkUsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BDLCtDQUFNQTt3Q0FDTHVDLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xDLFNBQVMsSUFBTXBDLG1CQUFtQjt3Q0FDbEMrQixXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNwQywrQ0FBTUE7d0NBQ0x1QyxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTYjt3Q0FDVFEsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDcEMsK0NBQU1BO3dDQUNMdUMsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEMsU0FBU2hCO3dDQUNUVyxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU1JoQyxpQ0FDQyw4REFBQytCO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDTTtnQ0FBR04sV0FBVTswQ0FBbUQ7Ozs7OzswQ0FHakUsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFpQzs7Ozs7OzBDQUs5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUdELFdBQVU7a0VBQXlDOzs7Ozs7a0VBQ3ZELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0U7Ozs7Ozs7Ozs7OzswREFJckYsOERBQUNFO2dEQUFFRixXQUFVOzBEQUFvQzs7Ozs7Ozs7Ozs7O2tEQU1uRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFHRCxXQUFVO2tFQUF5Qzs7Ozs7O2tFQUN2RCw4REFBQ087d0RBQU1QLFdBQVU7OzBFQUNmLDhEQUFDUTtnRUFDQ1gsTUFBSztnRUFDTFksU0FBU3ZDLFlBQVlHLFNBQVM7Z0VBQzlCcUMsVUFBVSxJQUFNZCx1QkFBdUI7Z0VBQ3ZDSSxXQUFVOzs7Ozs7MEVBRVosOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR25CLDhEQUFDRTtnREFBRUYsV0FBVTswREFBb0M7Ozs7Ozs7Ozs7OztrREFNbkQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBR0QsV0FBVTtrRUFBeUM7Ozs7OztrRUFDdkQsOERBQUNPO3dEQUFNUCxXQUFVOzswRUFDZiw4REFBQ1E7Z0VBQ0NYLE1BQUs7Z0VBQ0xZLFNBQVN2QyxZQUFZSSxTQUFTO2dFQUM5Qm9DLFVBQVUsSUFBTWQsdUJBQXVCO2dFQUN2Q0ksV0FBVTs7Ozs7OzBFQUVaLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUduQiw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXJELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNwQywrQ0FBTUE7d0NBQ0x1QyxTQUFRO3dDQUNSRSxTQUFTLElBQU1wQyxtQkFBbUI7d0NBQ2xDK0IsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDcEMsK0NBQU1BO3dDQUNMdUMsU0FBUTt3Q0FDUkUsU0FBU1o7d0NBQ1RPLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQjtBQUVBLGlFQUFlbkMsYUFBYUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnaW92YVxcRGVza3RvcFxcUHJvZ2V0dGluaVxcb3R0aWNhLWdyMVxcb3R0aWNhLWdyMVxcY29tcG9uZW50c1xcdWlcXENvb2tpZUNvbnNlbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBCdXR0b24gZnJvbSAnLi9CdXR0b24nO1xyXG5cclxuaW50ZXJmYWNlIENvb2tpZVByZWZlcmVuY2VzIHtcclxuICBuZWNlc3Nhcnk6IGJvb2xlYW47XHJcbiAgYW5hbHl0aWNzOiBib29sZWFuO1xyXG4gIG1hcmtldGluZzogYm9vbGVhbjtcclxufVxyXG5cclxuY29uc3QgQ29va2llQ29uc2VudCA9ICgpID0+IHtcclxuICBjb25zdCBbc2hvd0Jhbm5lciwgc2V0U2hvd0Jhbm5lcl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dQcmVmZXJlbmNlcywgc2V0U2hvd1ByZWZlcmVuY2VzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcHJlZmVyZW5jZXMsIHNldFByZWZlcmVuY2VzXSA9IHVzZVN0YXRlPENvb2tpZVByZWZlcmVuY2VzPih7XHJcbiAgICBuZWNlc3Nhcnk6IHRydWUsIC8vIEFsd2F5cyB0cnVlLCBjYW5ub3QgYmUgZGlzYWJsZWRcclxuICAgIGFuYWx5dGljczogZmFsc2UsXHJcbiAgICBtYXJrZXRpbmc6IGZhbHNlLFxyXG4gIH0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgYWxyZWFkeSBtYWRlIGEgY2hvaWNlXHJcbiAgICBjb25zdCBjb25zZW50ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ290dGljYS1ncjEtY29va2llLWNvbnNlbnQnKTtcclxuICAgIGlmICghY29uc2VudCkge1xyXG4gICAgICBzZXRTaG93QmFubmVyKHRydWUpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc3Qgc2F2ZWRQcmVmZXJlbmNlcyA9IEpTT04ucGFyc2UoY29uc2VudCk7XHJcbiAgICAgIHNldFByZWZlcmVuY2VzKHNhdmVkUHJlZmVyZW5jZXMpO1xyXG4gICAgICAvLyBJbml0aWFsaXplIEdvb2dsZSBDb25zZW50IE1vZGUgYmFzZWQgb24gc2F2ZWQgcHJlZmVyZW5jZXNcclxuICAgICAgaW5pdGlhbGl6ZUdvb2dsZUNvbnNlbnQoc2F2ZWRQcmVmZXJlbmNlcyk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBpbml0aWFsaXplR29vZ2xlQ29uc2VudCA9IChwcmVmczogQ29va2llUHJlZmVyZW5jZXMpID0+IHtcclxuICAgIC8vIEdvb2dsZSBDb25zZW50IE1vZGUgdjIgaW1wbGVtZW50YXRpb25cclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93IGFzIGFueSkuZ3RhZykge1xyXG4gICAgICAod2luZG93IGFzIGFueSkuZ3RhZygnY29uc2VudCcsICd1cGRhdGUnLCB7XHJcbiAgICAgICAgYW5hbHl0aWNzX3N0b3JhZ2U6IHByZWZzLmFuYWx5dGljcyA/ICdncmFudGVkJyA6ICdkZW5pZWQnLFxyXG4gICAgICAgIGFkX3N0b3JhZ2U6IHByZWZzLm1hcmtldGluZyA/ICdncmFudGVkJyA6ICdkZW5pZWQnLFxyXG4gICAgICAgIGFkX3VzZXJfZGF0YTogcHJlZnMubWFya2V0aW5nID8gJ2dyYW50ZWQnIDogJ2RlbmllZCcsXHJcbiAgICAgICAgYWRfcGVyc29uYWxpemF0aW9uOiBwcmVmcy5tYXJrZXRpbmcgPyAnZ3JhbnRlZCcgOiAnZGVuaWVkJyxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQWNjZXB0QWxsID0gKCkgPT4ge1xyXG4gICAgY29uc3QgbmV3UHJlZmVyZW5jZXMgPSB7XHJcbiAgICAgIG5lY2Vzc2FyeTogdHJ1ZSxcclxuICAgICAgYW5hbHl0aWNzOiB0cnVlLFxyXG4gICAgICBtYXJrZXRpbmc6IHRydWUsXHJcbiAgICB9O1xyXG4gICAgc2F2ZVByZWZlcmVuY2VzKG5ld1ByZWZlcmVuY2VzKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSZWplY3RBbGwgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBuZXdQcmVmZXJlbmNlcyA9IHtcclxuICAgICAgbmVjZXNzYXJ5OiB0cnVlLFxyXG4gICAgICBhbmFseXRpY3M6IGZhbHNlLFxyXG4gICAgICBtYXJrZXRpbmc6IGZhbHNlLFxyXG4gICAgfTtcclxuICAgIHNhdmVQcmVmZXJlbmNlcyhuZXdQcmVmZXJlbmNlcyk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2F2ZVByZWZlcmVuY2VzID0gKCkgPT4ge1xyXG4gICAgc2F2ZVByZWZlcmVuY2VzKHByZWZlcmVuY2VzKTtcclxuICAgIHNldFNob3dQcmVmZXJlbmNlcyhmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2F2ZVByZWZlcmVuY2VzID0gKHByZWZzOiBDb29raWVQcmVmZXJlbmNlcykgPT4ge1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ290dGljYS1ncjEtY29va2llLWNvbnNlbnQnLCBKU09OLnN0cmluZ2lmeShwcmVmcykpO1xyXG4gICAgc2V0UHJlZmVyZW5jZXMocHJlZnMpO1xyXG4gICAgc2V0U2hvd0Jhbm5lcihmYWxzZSk7XHJcbiAgICBpbml0aWFsaXplR29vZ2xlQ29uc2VudChwcmVmcyk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlUHJlZmVyZW5jZUNoYW5nZSA9ICh0eXBlOiBrZXlvZiBDb29raWVQcmVmZXJlbmNlcykgPT4ge1xyXG4gICAgaWYgKHR5cGUgPT09ICduZWNlc3NhcnknKSByZXR1cm47IC8vIENhbm5vdCBkaXNhYmxlIG5lY2Vzc2FyeSBjb29raWVzXHJcbiAgICBzZXRQcmVmZXJlbmNlcyhwcmV2ID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIFt0eXBlXTogIXByZXZbdHlwZV0sXHJcbiAgICB9KSk7XHJcbiAgfTtcclxuXHJcbiAgaWYgKCFzaG93QmFubmVyKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHsvKiBDb29raWUgQmFubmVyICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIHotNTAgYmctd2hpdGUgYm9yZGVyLXQgYm9yZGVyLWJvcmRlci1ncmF5IHNoYWRvdy1sZ1wiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS02XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbGc6ZmxleC1yb3cgaXRlbXMtc3RhcnQgbGc6aXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBnYXAtNFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgZm9udC1zYW5zIHRleHQtdGV4dC1iYXNlIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgIFV0aWxpenppYW1vIGkgQ29va2llXHJcbiAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1iYXNlIG9wYWNpdHktODAgbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgICBVdGlsaXp6aWFtbyBjb29raWUgbmVjZXNzYXJpIHBlciBpbCBmdW56aW9uYW1lbnRvIGRlbCBzaXRvIGUgY29va2llIG9wemlvbmFsaSBwZXIgYW5hbGlzaSBlIG1hcmtldGluZy4gXHJcbiAgICAgICAgICAgICAgICBQdW9pIHNjZWdsaWVyZSBxdWFsaSBhY2NldHRhcmUgbyByaWZpdXRhcmUgdHV0dGkgaSBjb29raWUgb3B6aW9uYWxpLlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtMyBtaW4tdy1maXRcIj5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1ByZWZlcmVuY2VzKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgUGVyc29uYWxpenphXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJlamVjdEFsbH1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc21cIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIFJpZml1dGEgVHV0dG9cclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWNjZXB0QWxsfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQWNjZXR0YSBUdXR0b1xyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBQcmVmZXJlbmNlcyBNb2RhbCAqL31cclxuICAgICAge3Nob3dQcmVmZXJlbmNlcyAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNjAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBtYXgtdy0yeGwgdy1mdWxsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG9cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIGZvbnQtc2FucyB0ZXh0LXRleHQtYmFzZSBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICBQcmVmZXJlbnplIENvb2tpZVxyXG4gICAgICAgICAgICAgIDwvaDI+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0LWJhc2Ugb3BhY2l0eS04MCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICBHZXN0aXNjaSBsZSB0dWUgcHJlZmVyZW56ZSBzdWkgY29va2llLiBJIGNvb2tpZSBuZWNlc3Nhcmkgc29ubyBzZW1wcmUgYXR0aXZpIHBlciBnYXJhbnRpcmUgXHJcbiAgICAgICAgICAgICAgICBpbCBjb3JyZXR0byBmdW56aW9uYW1lbnRvIGRlbCBzaXRvLlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgIHsvKiBOZWNlc3NhcnkgQ29va2llcyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ib3JkZXItZ3JheSByb3VuZGVkLWxnIHAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgZm9udC1zYW5zIHRleHQtdGV4dC1iYXNlXCI+Q29va2llIE5lY2Vzc2FyaTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgU2VtcHJlIEF0dGl2aVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtYmFzZSBvcGFjaXR5LTgwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgUXVlc3RpIGNvb2tpZSBzb25vIGVzc2VuemlhbGkgcGVyIGlsIGZ1bnppb25hbWVudG8gZGVsIHNpdG8gd2ViIGUgbm9uIHBvc3Nvbm8gZXNzZXJlIGRpc2FiaWxpdGF0aS5cclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIEFuYWx5dGljcyBDb29raWVzICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWJvcmRlci1ncmF5IHJvdW5kZWQtbGcgcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBmb250LXNhbnMgdGV4dC10ZXh0LWJhc2VcIj5Db29raWUgQW5hbGl0aWNpPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17cHJlZmVyZW5jZXMuYW5hbHl0aWNzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4gaGFuZGxlUHJlZmVyZW5jZUNoYW5nZSgnYW5hbHl0aWNzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtNiBiZy1ncmF5LTIwMCBwZWVyLWZvY3VzOm91dGxpbmUtbm9uZSBwZWVyLWZvY3VzOnJpbmctNCBwZWVyLWZvY3VzOnJpbmctcHJpbWFyeS8yMCByb3VuZGVkLWZ1bGwgcGVlciBwZWVyLWNoZWNrZWQ6YWZ0ZXI6dHJhbnNsYXRlLXgtZnVsbCBwZWVyLWNoZWNrZWQ6YWZ0ZXI6Ym9yZGVyLXdoaXRlIGFmdGVyOmNvbnRlbnQtWycnXSBhZnRlcjphYnNvbHV0ZSBhZnRlcjp0b3AtWzJweF0gYWZ0ZXI6bGVmdC1bMnB4XSBhZnRlcjpiZy13aGl0ZSBhZnRlcjpyb3VuZGVkLWZ1bGwgYWZ0ZXI6aC01IGFmdGVyOnctNSBhZnRlcjp0cmFuc2l0aW9uLWFsbCBwZWVyLWNoZWNrZWQ6YmctcHJpbWFyeVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1iYXNlIG9wYWNpdHktODBcIj5cclxuICAgICAgICAgICAgICAgICAgICBDaSBhaXV0YW5vIGEgY2FwaXJlIGNvbWUgaSB2aXNpdGF0b3JpIGludGVyYWdpc2Nvbm8gY29uIGlsIHNpdG8gcmFjY29nbGllbmRvIGluZm9ybWF6aW9uaSBhbm9uaW1lLlxyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogTWFya2V0aW5nIENvb2tpZXMgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItYm9yZGVyLWdyYXkgcm91bmRlZC1sZyBwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIGZvbnQtc2FucyB0ZXh0LXRleHQtYmFzZVwiPkNvb2tpZSBNYXJrZXRpbmc8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtwcmVmZXJlbmNlcy5tYXJrZXRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiBoYW5kbGVQcmVmZXJlbmNlQ2hhbmdlKCdtYXJrZXRpbmcnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktMjAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHBlZXItZm9jdXM6cmluZy00IHBlZXItZm9jdXM6cmluZy1wcmltYXJ5LzIwIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1wcmltYXJ5XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC10ZXh0LWJhc2Ugb3BhY2l0eS04MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIFV0aWxpenphdGkgcGVyIG1vc3RyYXJlIGFubnVuY2kgcHViYmxpY2l0YXJpIHBpw7kgcmlsZXZhbnRpIHBlciB0ZSBlIGkgdHVvaSBpbnRlcmVzc2kuXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTMgbXQtOFwiPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQcmVmZXJlbmNlcyhmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIEFubnVsbGFcclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmVQcmVmZXJlbmNlc31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgU2FsdmEgUHJlZmVyZW56ZVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ29va2llQ29uc2VudDtcclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ29va2llQ29uc2VudCIsInNob3dCYW5uZXIiLCJzZXRTaG93QmFubmVyIiwic2hvd1ByZWZlcmVuY2VzIiwic2V0U2hvd1ByZWZlcmVuY2VzIiwicHJlZmVyZW5jZXMiLCJzZXRQcmVmZXJlbmNlcyIsIm5lY2Vzc2FyeSIsImFuYWx5dGljcyIsIm1hcmtldGluZyIsImNvbnNlbnQiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwic2F2ZWRQcmVmZXJlbmNlcyIsIkpTT04iLCJwYXJzZSIsImluaXRpYWxpemVHb29nbGVDb25zZW50IiwicHJlZnMiLCJ3aW5kb3ciLCJndGFnIiwiYW5hbHl0aWNzX3N0b3JhZ2UiLCJhZF9zdG9yYWdlIiwiYWRfdXNlcl9kYXRhIiwiYWRfcGVyc29uYWxpemF0aW9uIiwiaGFuZGxlQWNjZXB0QWxsIiwibmV3UHJlZmVyZW5jZXMiLCJzYXZlUHJlZmVyZW5jZXMiLCJoYW5kbGVSZWplY3RBbGwiLCJoYW5kbGVTYXZlUHJlZmVyZW5jZXMiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiaGFuZGxlUHJlZmVyZW5jZUNoYW5nZSIsInR5cGUiLCJwcmV2IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwiaDIiLCJsYWJlbCIsImlucHV0IiwiY2hlY2tlZCIsIm9uQ2hhbmdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/CookieConsent.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/WhatsAppButton.tsx":
/*!******************************************!*\
  !*** ./components/ui/WhatsAppButton.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst WhatsAppButton = ()=>{\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const phoneNumber = '+393928480621';\n    const message = 'Buongiorno vorrei prenotare un appuntamento';\n    const handleWhatsAppClick = ()=>{\n        const encodedMessage = encodeURIComponent(message);\n        const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-24 right-6 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleWhatsAppClick,\n            onMouseEnter: ()=>setIsHovered(true),\n            onMouseLeave: ()=>setIsHovered(false),\n            className: \"group relative bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110\",\n            \"aria-label\": \"Contattaci su WhatsApp\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"28\",\n                    height: \"28\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    className: \"transition-transform duration-300\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                isHovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                    children: [\n                        \"Scrivici su WhatsApp\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatsAppButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/WhatsAppButton.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/CookieConsent.tsx */ \"(ssr)/./components/ui/CookieConsent.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/WhatsAppButton.tsx */ \"(ssr)/./components/ui/WhatsAppButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Cui%5C%5CWhatsAppButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Source_Serif_4%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-source-serif%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22sourceSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dpb3ZhJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpbmklNUMlNUNvdHRpY2EtZ3IxJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q2FwcCU1QyU1Q25vdC1mb3VuZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ2lvdmFcXFxcRGVza3RvcFxcXFxQcm9nZXR0aW5pXFxcXG90dGljYS1ncjFcXFxcb3R0aWNhLWdyMVxcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(ssr)/./components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dpb3ZhJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpbmklNUMlNUNvdHRpY2EtZ3IxJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNIZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNnaW92YSU1QyU1Q0Rlc2t0b3AlNUMlNUNQcm9nZXR0aW5pJTVDJTVDb3R0aWNhLWdyMSU1QyU1Q290dGljYS1ncjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2FwcC1kaXIlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUErSjtBQUMvSjtBQUNBLGdOQUFnTSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGdpb3ZhXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGluaVxcXFxvdHRpY2EtZ3IxXFxcXG90dGljYS1ncjFcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGdpb3ZhXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGluaVxcXFxvdHRpY2EtZ3IxXFxcXG90dGljYS1ncjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgiova%5C%5CDesktop%5C%5CProgettini%5C%5Cottica-gr1%5C%5Cottica-gr1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprivacy%2Fpage&page=%2Fprivacy%2Fpage&appPaths=%2Fprivacy%2Fpage&pagePath=private-next-app-dir%2Fprivacy%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgiova%5CDesktop%5CProgettini%5Cottica-gr1%5Cottica-gr1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();