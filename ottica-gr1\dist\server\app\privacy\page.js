(()=>{var e={};e.id=877,e.ids=[877],e.modules={273:(e,t,i)=>{"use strict";i.d(t,{A:()=>o});var a=i(7413),s=i(4536),r=i.n(s);let o=()=>{let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,a.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,a.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,a.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,a.jsx)("p",{children:"✉️ <EMAIL>"}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,a.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(r(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,a.jsx)(r(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},418:(e,t,i)=>{"use strict";function a({title:e,description:t,keywords:i="",canonical:a,ogImage:s="/images/og-default.jpg",ogType:r="website",twitterCard:o="summary_large_image",noindex:n=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=a?`${l}${a}`:void 0,p=s.startsWith("http")?s:`${l}${s}`;return{title:c,description:t,keywords:i||void 0,robots:n?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:p,width:1200,height:630,alt:e}],locale:"it_IT",type:r},twitter:{card:o,title:c,description:t,images:[p],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>s,Y:()=>a});let s=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4047:(e,t,i)=>{Promise.resolve().then(i.bind(i,3121)),Promise.resolve().then(i.t.bind(i,5814,23))},4162:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n,metadata:()=>o});var a=i(7413),s=i(5271),r=i(273);let o=(0,i(418).Y)({title:"Privacy Policy - Ottica GR1",description:"Informativa sulla privacy di Ottica GR1. Come raccogliamo, utilizziamo e proteggiamo i tuoi dati personali.",keywords:"privacy policy, protezione dati, gdpr, ottica gr1, informativa privacy",canonical:"/privacy"});function n(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold font-sans text-primary mb-8",children:"Privacy Policy"}),(0,a.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,a.jsxs)("p",{className:"text-lg text-text-base mb-8",children:["Ultimo aggiornamento: ",new Date().toLocaleDateString("it-IT")]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"1. Introduzione"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:'Ottica GR1 (di seguito "noi", "nostro" o "la Societ\xe0") rispetta la tua privacy e si impegna a proteggere i tuoi dati personali. Questa informativa sulla privacy ti informa su come trattiamo i tuoi dati personali quando visiti il nostro sito web e ti informa sui tuoi diritti in materia di privacy e su come la legge ti protegge.'})]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"2. Dati che raccogliamo"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:"Possiamo raccogliere, utilizzare, archiviare e trasferire diversi tipi di dati personali su di te:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 text-text-base mb-4",children:[(0,a.jsx)("li",{children:"Dati di identit\xe0: nome, cognome"}),(0,a.jsx)("li",{children:"Dati di contatto: indirizzo email, numero di telefono"}),(0,a.jsx)("li",{children:"Dati tecnici: indirizzo IP, tipo di browser, fuso orario"}),(0,a.jsx)("li",{children:"Dati di utilizzo: informazioni su come utilizzi il nostro sito web"})]})]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"3. Come utilizziamo i tuoi dati"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:"Utilizziamo i tuoi dati personali per:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 text-text-base mb-4",children:[(0,a.jsx)("li",{children:"Rispondere alle tue richieste di informazioni"}),(0,a.jsx)("li",{children:"Prenotare appuntamenti per controlli della vista"}),(0,a.jsx)("li",{children:"Migliorare il nostro sito web e i nostri servizi"}),(0,a.jsx)("li",{children:"Inviarti comunicazioni di marketing (solo con il tuo consenso)"})]})]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"4. I tuoi diritti"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:"In base al GDPR, hai i seguenti diritti:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 text-text-base mb-4",children:[(0,a.jsx)("li",{children:"Diritto di accesso ai tuoi dati personali"}),(0,a.jsx)("li",{children:"Diritto di rettifica dei tuoi dati personali"}),(0,a.jsx)("li",{children:"Diritto di cancellazione dei tuoi dati personali"}),(0,a.jsx)("li",{children:"Diritto di limitazione del trattamento"}),(0,a.jsx)("li",{children:"Diritto alla portabilit\xe0 dei dati"}),(0,a.jsx)("li",{children:"Diritto di opposizione al trattamento"})]})]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"5. Sicurezza dei dati"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:"Abbiamo messo in atto misure di sicurezza appropriate per prevenire la perdita accidentale, l'uso o l'accesso non autorizzato, l'alterazione e la divulgazione dei tuoi dati personali."})]}),(0,a.jsxs)("section",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"6. Contatti"}),(0,a.jsx)("p",{className:"text-text-base mb-4",children:"Se hai domande su questa informativa sulla privacy o sui tuoi dati personali, contattaci:"}),(0,a.jsxs)("div",{className:"bg-background p-6 rounded-lg border border-border-gray",children:[(0,a.jsxs)("p",{className:"text-text-base mb-2",children:[(0,a.jsx)("strong",{children:"Titolare dei dati:"})," Antonio Gualerzi"]}),(0,a.jsxs)("p",{className:"text-text-base mb-2",children:[(0,a.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,a.jsxs)("p",{className:"text-text-base mb-2",children:[(0,a.jsx)("strong",{children:"Telefono:"}),"06 8862962 | +39 3928480621"]}),(0,a.jsxs)("p",{className:"text-text-base",children:[(0,a.jsx)("strong",{children:"Indirizzo:"})," Via Conca D'Oro, 323, 00141 ROMA RM"]})]})]})]})]})})}),(0,a.jsx)(r.A,{})]})}},4536:(e,t,i)=>{let{createProxy:a}=i(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4727:(e,t,i)=>{Promise.resolve().then(i.bind(i,5271)),Promise.resolve().then(i.t.bind(i,4536,23))},5271:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},5357:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=i(5239),s=i(8088),r=i(8170),o=i.n(r),n=i(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(t,l);let c={children:["",{children:["privacy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,4162)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\privacy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\privacy\\page.tsx"],p={require:i,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/privacy/page",pathname:"/privacy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,988,926],()=>i(5357));module.exports=a})();