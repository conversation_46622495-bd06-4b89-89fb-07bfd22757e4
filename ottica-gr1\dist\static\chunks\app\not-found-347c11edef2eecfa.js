(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{1024:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var s=t(5155),i=t(3343),n=t(8317),l=t(4912);function c(){return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("main",{className:"pt-20",children:(0,s.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background min-h-[80vh] flex items-center",children:(0,s.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsx)("div",{className:"text-8xl font-bold text-primary mb-8",children:"404"}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold font-sans text-text-base mb-6",children:"Pagina Non Trovata"}),(0,s.jsx)("p",{className:"text-xl text-text-base opacity-80 mb-8 leading-relaxed",children:"Ci dispiace, la pagina che stai cercando non esiste o \xe8 stata spostata."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(l.A,{variant:"primary",size:"lg",onClick:()=>window.location.href="/",children:"Torna alla Homepage"}),(0,s.jsx)(l.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="/contatti",children:"Contattaci"})]})]})})})}),(0,s.jsx)(n.A,{})]})}},9027:(e,a,t)=>{Promise.resolve().then(t.bind(t,1024))}},e=>{var a=a=>e(e.s=a);e.O(0,[261,350,441,684,358],()=>a(9027)),_N_E=e.O()}]);