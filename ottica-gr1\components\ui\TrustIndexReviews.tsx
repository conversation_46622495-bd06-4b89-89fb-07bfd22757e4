'use client';

import { useEffect, useRef, useState } from 'react';

interface TrustIndexReviewsProps {
  className?: string;
}

export default function TrustIndexReviews({ className = '' }: TrustIndexReviewsProps) {
  const widgetRef = useRef<HTMLDivElement>(null);
  const [showFallback, setShowFallback] = useState(false);

  useEffect(() => {
    if (widgetRef.current && typeof window !== 'undefined') {
      // Clear any existing content
      widgetRef.current.innerHTML = '';

      // Method 1: Try the standard TrustIndex approach
      const widgetDiv = document.createElement('div');
      widgetDiv.setAttribute('src', 'https://cdn.trustindex.io/loader.js?a5b0a554919a854f166675fc15f');
      widgetRef.current.appendChild(widgetDiv);

      // Method 2: Try direct script injection
      const script = document.createElement('script');
      script.src = 'https://cdn.trustindex.io/loader.js?a5b0a554919a854f166675fc15f';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log('TrustIndex widget script loaded');
        // Try to initialize
        if ((window as any).trustindex && typeof (window as any).trustindex.init === 'function') {
          (window as any).trustindex.init();
        }
      };

      script.onerror = () => {
        console.log('TrustIndex script failed to load, showing fallback');
        setShowFallback(true);
      };

      // Append script to head
      document.head.appendChild(script);

      // Fallback timer
      const fallbackTimer = setTimeout(() => {
        if (!widgetRef.current?.querySelector('iframe') && !widgetRef.current?.querySelector('.trustindex-widget')) {
          console.log('TrustIndex widget not loaded after 5 seconds, showing fallback');
          setShowFallback(true);
        }
      }, 5000);

      return () => {
        clearTimeout(fallbackTimer);
        // Clean up script
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      };
    }
  }, []);

  return (
    <section className={`py-12 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Cosa dicono i nostri clienti
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Scopri le recensioni dei nostri clienti soddisfatti e la loro esperienza con Ottica GR1
          </p>
        </div>

        {/* TrustIndex Reviews Widget */}
        <div className="flex justify-center">
          {showFallback ? (
            <div className="w-full max-w-4xl bg-white rounded-lg shadow-md p-8 text-center">
              <div className="mb-6">
                <div className="flex justify-center mb-4">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-6 h-6 text-yellow-400 fill-current" viewBox="0 0 20 20">
                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                      </svg>
                    ))}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Recensioni Google
                </h3>
                <p className="text-gray-600 mb-4">
                  I nostri clienti ci hanno valutato con 5 stelle su Google
                </p>
                <a
                  href="https://www.google.com/search?q=ottica+gr1+montesacro+roma&rlz=1C1CHBF_itIT1000IT1000&oq=ottica+gr1&aqs=chrome.0.69i59j46i175i199i512j0i512l2j69i60l4.2071j0j7&sourceid=chrome&ie=UTF-8#lrd=0x132f66b2ed4f3b67:0xdbd9c055e4803223,1"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Vedi le nostre recensioni su Google
                </a>
              </div>
            </div>
          ) : (
            <div
              ref={widgetRef}
              className="w-full max-w-4xl min-h-[200px] flex items-center justify-center"
            >
              <div className="text-center text-gray-500">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                <p>Caricamento recensioni...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
