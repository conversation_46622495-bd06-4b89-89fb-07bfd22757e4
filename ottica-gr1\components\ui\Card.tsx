import { ReactNode } from 'react';
import Image from 'next/image';

interface CardProps {
  children?: ReactNode;
  className?: string;
  image?: string;
  imageAlt?: string;
  title?: string;
  description?: string;
  hover?: boolean;
  grayscaleHover?: boolean;
}

const Card = ({
  children,
  className = '',
  image,
  imageAlt = '',
  title,
  description,
  hover = true,
  grayscaleHover = false,
}: CardProps) => {
  const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden';
  const hoverClasses = hover ? 'transition-all duration-300 hover:shadow-xl hover:-translate-y-1' : '';
  const combinedClasses = `${baseClasses} ${hoverClasses} ${className}`.trim();

  return (
    <div className={combinedClasses}>
      {image && (
        <div className="relative h-48 w-full overflow-hidden">
          <Image
            src={image}
            alt={imageAlt}
            fill
            className={`object-cover transition-all duration-300 ${
              grayscaleHover ? 'grayscale-hover' : ''
            }`}
          />
        </div>
      )}
      
      {(title || description || children) && (
        <div className="p-6">
          {title && (
            <h3 className="text-xl font-semibold font-sans text-text-base mb-2">
              {title}
            </h3>
          )}
          
          {description && (
            <p className="text-text-base opacity-80 leading-relaxed mb-4">
              {description}
            </p>
          )}
          
          {children}
        </div>
      )}
    </div>
  );
};

export default Card;
