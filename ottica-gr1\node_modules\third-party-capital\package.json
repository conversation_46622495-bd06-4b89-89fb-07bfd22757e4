{"name": "third-party-capital", "version": "1.0.20", "description": "", "main": "./lib/cjs/index.js", "types": "./lib/cjs/types/index.d.ts", "files": ["lib/**/*"], "exports": {".": {"import": {"types": "./lib/esm/types/index.d.ts", "default": "./lib/esm/index.js"}, "require": {"types": "./lib/cjs/types/index.d.ts", "default": "./lib/cjs/index.js"}}}, "scripts": {"clean": "rm -rf ./lib", "build": "npm run clean && npm run build:esm && npm run build:cjs", "build:esm": "tsc -p ./config/tsconfig.esm.json && mv lib/esm/index.js lib/esm/index.js", "build:cjs": "tsc -p ./config/tsconfig.cjs.json", "prepack": "npm run build", "prepare": "husky install", "format:check": "prettier --check '**/*.{ts,js}'", "lint:check": "eslint '**/*.{ts,js}'", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/housseindjirdeh/third-party-capital.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/housseindjirdeh/third-party-capital/issues"}, "homepage": "https://github.com/housseindjirdeh/third-party-capital#readme", "devDependencies": {"@types/jest": "^29.5.1", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^13.2.1", "prettier": "2.8.7", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^4.7.4"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write --ignore-unknown"]}}