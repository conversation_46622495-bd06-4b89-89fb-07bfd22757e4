(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[677,877],{3343:(e,a,s)=>{"use strict";s.d(a,{default:()=>c});var t=s(5155),l=s(2115),r=s(6874),n=s.n(r),i=s(6766),o=s(5695);let c=()=>{let[e,a]=(0,l.useState)(!1),[s,r]=(0,l.useState)(!1),c=(0,o.usePathname)();(0,l.useEffect)(()=>{let e=()=>{a(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,l.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]);let d=[{href:"/",label:"HOME"},{href:"/storia",label:"STORIA"},{href:"/servizi",label:"SERVIZI"},{href:"/occhiali",label:"OCCHIALI DA VISTA"},{href:"/lenti-contatto",label:"LENTI A CONTATTO"},{href:"/occhiali-sole",label:"OCCHIALI DA SOLE"},{href:"/esami-vista",label:"ESAMI DELLA VISTA"},{href:"/contatti",label:"CONTATTI"}];return(0,t.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(e?"bg-white shadow-lg":"/"===c||s?"bg-white shadow-sm":"bg-white/90 shadow-sm"),children:(0,t.jsxs)("nav",{className:"container mx-auto px-4 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative h-10 w-10 flex-shrink-0",children:(0,t.jsx)(i.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain",priority:!0})}),(0,t.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,t.jsx)("span",{className:"text-primary transition-colors duration-300",children:"OTTICA"}),(0,t.jsx)("span",{className:"text-accent transition-colors duration-300 ml-1",children:"GR1"})]})]}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:d.map(e=>(0,t.jsx)(n(),{href:e.href,className:"font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ".concat(c===e.href?"text-primary":"text-text-base hover:text-primary"),children:e.label},e.href))}),(0,t.jsxs)("button",{className:"lg:hidden flex flex-col space-y-1 w-6 h-6",onClick:()=>r(!s),"aria-label":"Toggle mobile menu",children:[(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"rotate-45 translate-y-1.5":"")}),(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"opacity-0":"")}),(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"-rotate-45 -translate-y-1.5":"")})]})]}),s&&(0,t.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]",onClick:()=>r(!1)}),(0,t.jsx)("div",{className:"lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ".concat(s?"translate-x-0":"translate-x-full"),style:{backgroundColor:"#ffffff",backdropFilter:"none"},children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative h-8 w-8 flex-shrink-0",children:(0,t.jsx)(i.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain"})}),(0,t.jsxs)("div",{className:"text-lg font-bold font-sans",children:[(0,t.jsx)("span",{className:"text-primary",children:"OTTICA"}),(0,t.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]})]}),(0,t.jsx)("button",{onClick:()=>r(!1),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Close menu",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,t.jsx)("nav",{className:"flex-1 px-6 py-6",children:(0,t.jsx)("div",{className:"space-y-2",children:d.map(e=>(0,t.jsx)(n(),{href:e.href,className:"block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ".concat(c===e.href?"text-primary bg-primary/10 border-l-4 border-primary":"text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1"),onClick:()=>r(!1),children:e.label},e.href))})}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Dal 1982 a Montesacro"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Tradizione e innovazione"})]})})]})})]})})}},4473:(e,a,s)=>{Promise.resolve().then(s.bind(s,3343)),Promise.resolve().then(s.t.bind(s,6874,23))}},e=>{var a=a=>e(e.s=a);e.O(0,[261,441,684,358],()=>a(4473)),_N_E=e.O()}]);