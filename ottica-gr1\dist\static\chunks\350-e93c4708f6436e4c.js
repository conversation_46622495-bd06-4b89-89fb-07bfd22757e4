"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[350],{3343:(e,a,s)=>{s.d(a,{default:()=>o});var t=s(5155),i=s(2115),r=s(6874),l=s.n(r),c=s(6766),n=s(5695);let o=()=>{let[e,a]=(0,i.useState)(!1),[s,r]=(0,i.useState)(!1),o=(0,n.usePathname)();(0,i.useEffect)(()=>{let e=()=>{a(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,i.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]);let d=[{href:"/",label:"HOME"},{href:"/storia",label:"STORIA"},{href:"/servizi",label:"SERVIZI"},{href:"/occhiali",label:"OCCHIALI DA VISTA"},{href:"/lenti-contatto",label:"LENTI A CONTATTO"},{href:"/occhiali-sole",label:"OCCHIALI DA SOLE"},{href:"/esami-vista",label:"ESAMI DELLA VISTA"},{href:"/contatti",label:"CONTATTI"}];return(0,t.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(e?"bg-white shadow-lg":"/"===o||s?"bg-white shadow-sm":"bg-white/90 shadow-sm"),children:(0,t.jsxs)("nav",{className:"container mx-auto px-4 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative h-10 w-10 flex-shrink-0",children:(0,t.jsx)(c.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain",priority:!0})}),(0,t.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,t.jsx)("span",{className:"text-primary transition-colors duration-300",children:"OTTICA"}),(0,t.jsx)("span",{className:"text-accent transition-colors duration-300 ml-1",children:"GR1"})]})]}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:d.map(e=>(0,t.jsx)(l(),{href:e.href,className:"font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ".concat(o===e.href?"text-primary":"text-text-base hover:text-primary"),children:e.label},e.href))}),(0,t.jsxs)("button",{className:"lg:hidden flex flex-col space-y-1 w-6 h-6",onClick:()=>r(!s),"aria-label":"Toggle mobile menu",children:[(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"rotate-45 translate-y-1.5":"")}),(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"opacity-0":"")}),(0,t.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(s?"-rotate-45 -translate-y-1.5":"")})]})]}),s&&(0,t.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]",onClick:()=>r(!1)}),(0,t.jsx)("div",{className:"lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ".concat(s?"translate-x-0":"translate-x-full"),style:{backgroundColor:"#ffffff",backdropFilter:"none"},children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative h-8 w-8 flex-shrink-0",children:(0,t.jsx)(c.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain"})}),(0,t.jsxs)("div",{className:"text-lg font-bold font-sans",children:[(0,t.jsx)("span",{className:"text-primary",children:"OTTICA"}),(0,t.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]})]}),(0,t.jsx)("button",{onClick:()=>r(!1),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Close menu",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,t.jsx)("nav",{className:"flex-1 px-6 py-6",children:(0,t.jsx)("div",{className:"space-y-2",children:d.map(e=>(0,t.jsx)(l(),{href:e.href,className:"block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ".concat(o===e.href?"text-primary bg-primary/10 border-l-4 border-primary":"text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1"),onClick:()=>r(!1),children:e.label},e.href))})}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Dal 1982 a Montesacro"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Tradizione e innovazione"})]})})]})})]})})}},4912:(e,a,s)=>{s.d(a,{A:()=>i});var t=s(5155);let i=e=>{let{children:a,variant:s="primary",size:i="md",fullWidth:r=!1,className:l="",...c}=e,n="".concat("font-sans font-medium transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({primary:"bg-primary text-white hover:bg-primary-dark focus:ring-primary",secondary:"bg-gray-200 text-text-base hover:bg-gray-300 focus:ring-gray-400",accent:"bg-accent text-white hover:bg-accent-light focus:ring-accent",outline:"border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary"}[s]," ").concat({sm:"px-3 py-1.5 text-sm",md:"px-6 py-2.5 text-base",lg:"px-8 py-3 text-lg"}[i]," ").concat(r?"w-full":""," ").concat(l).trim();return(0,t.jsx)("button",{className:n,...c,children:a})}},8317:(e,a,s)=>{s.d(a,{A:()=>l});var t=s(5155),i=s(6874),r=s.n(i);let l=()=>{let e=new Date().getFullYear();return(0,t.jsx)("footer",{className:"bg-primary text-white",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,t.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,t.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,t.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(r(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,t.jsx)("li",{children:(0,t.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,t.jsx)("li",{children:(0,t.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,t.jsx)("li",{children:(0,t.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,t.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,t.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,t.jsx)("p",{children:"✉️ <EMAIL>"}),(0,t.jsxs)("div",{className:"pt-2",children:[(0,t.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,t.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,t.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,t.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,t.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,t.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,t.jsxs)("div",{className:"flex space-x-6",children:[(0,t.jsx)(r(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,t.jsx)(r(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}}}]);