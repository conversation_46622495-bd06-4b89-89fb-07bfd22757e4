(()=>{var e={};e.id=876,e.ids=[876],e.modules={255:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n,metadata:()=>s});var a=i(7413),o=i(5777);let s=(0,i(418).Y)({title:"Lenti a Contatto - Giornaliere, Mensili e Specialistiche",description:"Lenti a contatto di ultima generazione a Montesacro. Johnson & Johnson, CooperVision, Bausch+Lomb. Giornaliere, mensili, toriche e multifocali.",keywords:"lenti a contatto roma, lenti giornaliere, lenti mensili, lenti toriche, lenti multifocali, johnson johnson, coopervision, montesacro",canonical:"/lenti-contatto"});function n(){return(0,a.jsx)(o.default,{})}},418:(e,t,i)=>{"use strict";function a({title:e,description:t,keywords:i="",canonical:a,ogImage:o="/images/og-default.jpg",ogType:s="website",twitterCard:n="summary_large_image",noindex:r=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=a?`${l}${a}`:void 0,m=o.startsWith("http")?o:`${l}${o}`;return{title:c,description:t,keywords:i||void 0,robots:r?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:m,width:1200,height:630,alt:e}],locale:"it_IT",type:s},twitter:{card:n,title:c,description:t,images:[m],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>o,Y:()=>a});let o=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2693:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=i(5239),o=i(8088),s=i(8170),n=i.n(s),r=i(893),l={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>r[e]);i.d(t,l);let c={children:["",{children:["lenti-contatto",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,255)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\lenti-contatto\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\lenti-contatto\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/lenti-contatto/page",pathname:"/lenti-contatto",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3044:(e,t,i)=>{Promise.resolve().then(i.bind(i,4023))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4023:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var a=i(687),o=i(3121),s=i(919),n=i(4325),r=i(2294),l=i(474);function c(){let e=[{id:"1",src:"/images/marchi/lenti-contatto/JohnsonJohnson-logo.jpg",alt:"Johnson & Johnson"},{id:"2",src:"/images/marchi/lenti-contatto/CooperVision-logo.jpg",alt:"CooperVision"},{id:"3",src:"/images/marchi/lenti-contatto/Bauschelomb-logo.jpg",alt:"Bausch + Lomb"},{id:"4",src:"/images/marchi/lenti-contatto/Menicon-logo.jpg",alt:"Menicon"},{id:"5",src:"/images/marchi/lenti-contatto/Soleko-logo.jpg",alt:"Soleko"},{id:"6",src:"/images/marchi/lenti-contatto/Freeoptik-logo.jpg",alt:"Freeoptik"}];return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(o.default,{}),(0,a.jsxs)("main",{className:"pt-20",children:[(0,a.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-accent text-lg font-sans font-medium mb-4",children:"Comfort e Libert\xe0"}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:"Lenti a Contatto"}),(0,a.jsx)("p",{className:"text-xl text-text-base opacity-80 leading-relaxed mb-8",children:"Scopri la libert\xe0 di movimento con le nostre lenti a contatto di ultima generazione. Offriamo una vasta gamma di soluzioni per ogni esigenza: giornaliere, mensili, toriche e multifocali dei migliori brand internazionali."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(r.A,{variant:"primary",size:"lg",onClick:()=>window.location.href="/contatti",children:"Prova Gratuita Lenti"}),(0,a.jsx)(r.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="/esami-vista",children:"Controllo Vista"})]})]}),(0,a.jsx)("div",{className:"relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl",children:(0,a.jsx)(l.default,{src:"/images/section-cards/DSC09603.jpeg",alt:"Lenti a contatto di qualit\xe0",fill:!0,className:"object-cover"})})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Tipologie di Lenti"}),(0,a.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Soluzioni per ogni esigenza visiva e stile di vita"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Lenti Giornaliere",description:"Massima igiene e comfort con lenti usa e getta per ogni giorno della settimana.",icon:"\uD83D\uDCC5"},{title:"Lenti Mensili",description:"Soluzione economica e pratica per chi usa le lenti regolarmente.",icon:"\uD83D\uDDD3️"},{title:"Lenti Toriche",description:"Correzione specializzata per astigmatismo con stabilit\xe0 e nitidezza superiori.",icon:"\uD83D\uDC53"},{title:"Lenti Multifocali",description:"Visione perfetta a tutte le distanze per chi soffre di presbiopia.",icon:"\uD83D\uDD0D"},{title:"Liquido pulizia lenti",description:"Tieni pulite le lenti a contatto con la soluzione apposita.",icon:"​\uD83D\uDCA7​"},{title:"Consulenza Specializzata",description:"I nostri esperti ti guidano nella scelta delle lenti pi\xf9 adatte a te.",icon:"\uD83D\uDC68‍⚕️"}].map((e,t)=>(0,a.jsxs)("div",{className:"text-center p-6 bg-background rounded-lg hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,a.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-3",children:e.title}),(0,a.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed",children:e.description})]},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-8 text-center",children:"Le Nostre Lenti a Contatto e Soluzioni: Comfort e Chiarezza per Ogni Giorno"}),(0,a.jsxs)("div",{className:"prose prose-lg max-w-none text-text-base opacity-90 leading-relaxed space-y-6",children:[(0,a.jsx)("p",{children:"Per chi desidera una libert\xe0 visiva senza compromessi, offriamo un'ampia gamma di lenti a contatto e le relative soluzioni per la manutenzione, selezionate tra i marchi pi\xf9 affidabili e innovativi del settore. Sappiamo quanto sia importante il comfort, la sicurezza e la chiarezza visiva per i portatori di lenti a contatto, per questo collaboriamo solo con aziende leader che garantiscono prodotti di altissima qualit\xe0."}),(0,a.jsx)("p",{children:"Che tu cerchi lenti giornaliere, quindicinali o mensili, per miopia, ipermetropia, astigmatismo o presbiopia, e le soluzioni pi\xf9 adatte per la loro pulizia e conservazione, troverai la risposta alle tue esigenze. Il nostro team \xe8 a tua disposizione per consigliarti la soluzione migliore, assicurandoti il massimo benessere per i tuoi occhi."})]})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("div",{className:"text-center mb-12",children:(0,a.jsx)("h3",{className:"text-2xl md:text-3xl font-bold font-sans text-text-base mb-6",children:"I nostri marchi includono:"})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto",children:[{name:"Johnson & Johnson",description:"Leader mondiale nelle lenti a contatto innovative"},{name:"CooperVision",description:"Tecnologia avanzata per comfort superiore"},{name:"Bausch + Lomb",description:"Oltre 160 anni di esperienza nella cura della vista"},{name:"Alcon",description:"Soluzioni complete per la salute degli occhi"},{name:"Menicon",description:"Innovazione giapponese per lenti di qualit\xe0"},{name:"Soleko",description:"Eccellenza italiana nelle lenti a contatto"},{name:"Freeoptik",description:"Soluzioni personalizzate per ogni esigenza"}].map((e,t)=>(0,a.jsxs)("div",{className:"bg-background p-6 rounded-lg text-center hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold font-sans text-primary mb-3",children:e.name}),(0,a.jsx)("p",{className:"text-text-base opacity-80 text-sm",children:e.description})]},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-background",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"I Nostri Partner"}),(0,a.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Collaboriamo con i migliori marchi internazionali per offrirti lenti a contatto di qualit\xe0 superiore"})]}),e.length>0&&(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsx)(n.A,{items:e,autoPlay:!0,autoPlayInterval:3500,showDots:!0,showArrows:!0,itemsPerView:4,className:"brands-carousel carousel-container"})})]})}),(0,a.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Prova Gratuita Lenti a Contatto"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Scopri il comfort delle nostre lenti a contatto con una prova gratuita. I nostri esperti ti guideranno nella scelta perfetta per te."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(r.A,{variant:"accent",size:"lg",onClick:()=>window.location.href="/contatti",children:"Prenota Prova Gratuita"}),(0,a.jsx)(r.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="tel:068862962",className:"border-white text-white hover:bg-white hover:text-primary",children:"Chiama: 068862962"})]})]})})]}),(0,a.jsx)(s.A,{})]})}},4325:(e,t,i)=>{"use strict";i.d(t,{A:()=>l});var a=i(687),o=i(3210),s=i(474);let n={mobile:768,tablet:1024,desktop:1200},r=(e=n)=>{let[t,i]=(0,o.useState)({width:0,height:0});(0,o.useEffect)(()=>{let e=()=>{i({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[]);let a=t.width<e.mobile,s=t.width>=e.mobile&&t.width<e.tablet,r=t.width>=e.desktop,l=t.width>=e.tablet;return{windowSize:t,isMobile:a,isTablet:s,isDesktop:r,isLargeScreen:l,breakpoints:e}},l=({items:e,autoPlay:t=!0,autoPlayInterval:i=3e3,showDots:n=!0,showArrows:l=!0,itemsPerView:c=1,className:d=""})=>{let[m,p]=(0,o.useState)(0),[u,h]=(0,o.useState)(!1),[x,g]=(0,o.useState)(!1),f=(0,o.useRef)(null),{isMobile:v,isTablet:b}=r(),j=v?Math.min(2,c):b?Math.min(3,c):c,w=Math.ceil(e.length/j),y=(0,o.useCallback)(()=>{x||(g(!0),p(e=>(e+1)%w),setTimeout(()=>{g(!1)},600))},[x,w]),N=(0,o.useCallback)(()=>{x||(g(!0),p(e=>(e-1+w)%w),setTimeout(()=>{g(!1)},600))},[x,w]),z=(0,o.useCallback)(e=>{x||(g(!0),p(e),setTimeout(()=>{g(!1)},600))},[x]);return(0,o.useEffect)(()=>(t&&!u&&w>1&&!x&&(f.current=setInterval(()=>{y()},i)),()=>{f.current&&clearInterval(f.current)}),[t,i,u,w,x,y]),(0,a.jsxs)("div",{className:`relative w-full ${d}`,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:[(0,a.jsx)("div",{className:"relative overflow-hidden rounded-lg",children:(0,a.jsx)("div",{className:`flex transition-all duration-700 ease-out transform ${x?"scale-[0.98]":"scale-100"} ${v?"duration-500":"duration-700"}`,style:{transform:`translateX(-${100*m}%)`,willChange:"transform"},children:Array.from({length:w}).map((t,i)=>(0,a.jsx)("div",{className:"w-full flex-shrink-0",children:(0,a.jsx)("div",{className:`flex w-full ${j>1?"gap-4 px-2":""}`,children:e.slice(i*j,(i+1)*j).map((e,t)=>(0,a.jsx)("div",{className:`${j>1?"flex-1":"w-full"} relative group`,style:{animationDelay:`${100*t}ms`},children:(0,a.jsxs)("div",{className:"relative h-32 md:h-40 lg:h-48 w-full",children:[(0,a.jsx)("div",{className:"relative h-full w-full bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-500 p-4 flex items-center justify-center group-hover:scale-105 group-hover:bg-gray-50",children:(0,a.jsx)(s.default,{src:e.src,alt:e.alt,fill:!0,className:"object-contain transition-all duration-500 group-hover:scale-110 p-2 filter group-hover:brightness-110"})}),e.title&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-3 rounded-b-lg",children:(0,a.jsx)("h3",{className:"text-sm font-medium text-center",children:e.title})})]})},e.id))})},i))})}),l&&w>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:N,disabled:x,className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-10 disabled:opacity-50 disabled:cursor-not-allowed group","aria-label":"Previous slide",children:(0,a.jsx)("svg",{className:"w-5 h-5 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)("button",{onClick:y,disabled:x,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-10 disabled:opacity-50 disabled:cursor-not-allowed group","aria-label":"Next slide",children:(0,a.jsx)("svg",{className:"w-5 h-5 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M9 5l7 7-7 7"})})})]}),n&&w>1&&(0,a.jsx)("div",{className:"flex justify-center mt-8 space-x-3",children:Array.from({length:w}).map((e,t)=>(0,a.jsx)("button",{onClick:()=>z(t),disabled:x,className:`relative transition-all duration-500 disabled:cursor-not-allowed ${t===m?"w-8 h-3 bg-primary rounded-full":"w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full hover:scale-125"}`,"aria-label":`Go to slide ${t+1}`,children:t===m&&(0,a.jsx)("div",{className:"absolute inset-0 bg-primary rounded-full animate-pulse opacity-50"})},t))})]})}},5777:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\lenti-contatto\\\\LentiContattoContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\lenti-contatto\\LentiContattoContent.tsx","default")},6244:(e,t,i)=>{Promise.resolve().then(i.bind(i,5777))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,988,926],()=>i(2693));module.exports=a})();