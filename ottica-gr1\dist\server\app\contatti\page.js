(()=>{var e={};e.id=581,e.ids=[581],e.modules={136:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var a=i(687),s=i(474);let r=({children:e,className:t="",image:i,imageAlt:r="",title:o,description:n,hover:l=!0,grayscaleHover:c=!1})=>{let d=`bg-white rounded-lg shadow-md overflow-hidden ${l?"transition-all duration-300 hover:shadow-xl hover:-translate-y-1":""} ${t}`.trim();return(0,a.jsxs)("div",{className:d,children:[i&&(0,a.jsx)("div",{className:"relative h-48 w-full overflow-hidden",children:(0,a.jsx)(s.default,{src:i,alt:r,fill:!0,className:`object-cover transition-all duration-300 ${c?"grayscale-hover":""}`})}),(o||n||e)&&(0,a.jsxs)("div",{className:"p-6",children:[o&&(0,a.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-2",children:o}),n&&(0,a.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed mb-4",children:n}),e]})]})}},273:(e,t,i)=>{"use strict";i.d(t,{A:()=>o});var a=i(7413),s=i(4536),r=i.n(s);let o=()=>{let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,a.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,a.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,a.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,a.jsx)("p",{children:"✉️ <EMAIL>"}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,a.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(r(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,a.jsx)(r(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},296:(e,t,i)=>{"use strict";i.d(t,{default:()=>l});var a=i(687),s=i(3210),r=i(2294),o=i(136),n=i(5036);function l(){let[e,t]=(0,s.useState)({nome:"",email:"",telefono:"",servizio:"",messaggio:"",privacyConsent:!1}),[i,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)("idle"),[m,x]=(0,s.useState)(""),p=e=>{let i=e.target,{name:a,value:s,type:r}=i,o="checked"in i&&i.checked;t(e=>({...e,[a]:"checkbox"===r?o:s}))},h=async i=>{if(i.preventDefault(),l(!0),d("idle"),x(""),!e.privacyConsent){d("error"),x("\xc8 necessario accettare il trattamento dei dati personali per inviare il messaggio."),l(!1);return}try{let i=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await i.json();i.ok?(d("success"),(0,n.trackFormSubmission)("contact_form",!0),t({nome:"",email:"",telefono:"",servizio:"",messaggio:"",privacyConsent:!1})):(d("error"),(0,n.trackFormSubmission)("contact_form",!1),x(a.error||"Si \xe8 verificato un errore durante l'invio del messaggio."))}catch(e){d("error"),(0,n.trackFormSubmission)("contact_form",!1),x("Errore di connessione. Riprova pi\xf9 tardi.")}finally{l(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:"Contattaci"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-text-base opacity-80 leading-relaxed",children:"Siamo qui per aiutarti. Vieni a trovarci o contattaci per qualsiasi informazione."})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:[{icon:"\uD83D\uDCCD",title:"Indirizzo",details:["Via Conca D Oro, 323","00141 Roma (RM)"],action:"Vedi su Maps"},{icon:"\uD83D\uDCDE",title:"Telefono",details:["06 8862962","+39 3928480621  "],action:"Chiama ora"},{icon:"\uD83C\uDD7F️",title:"Parcheggio Convenzionato",details:["Via Val Maira 6","00141 Roma (RM)"],action:"Vedi su Maps"},{icon:"\uD83D\uDD52",title:"Orari di Apertura",details:["Lun: 16:00 - 19:30","Mar - Sab: 9:00 - 13:00","16:30 - 19:30","Dom: Chiuso"],action:null}].map((e,t)=>(0,a.jsx)(o.A,{className:"text-center h-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,a.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-4",children:e.title}),(0,a.jsx)("div",{className:"space-y-1 mb-4",children:e.details.map((e,t)=>(0,a.jsx)("p",{className:"text-text-base opacity-80",children:e},t))}),e.action&&(0,a.jsx)(r.A,{variant:"outline",size:"sm",onClick:()=>{"Telefono"===e.title?window.location.href="tel:068862962":"Parcheggio Convenzionato"===e.title?window.open("https://maps.google.com/?q=Via+Val+Maira+6+Roma","_blank"):"Indirizzo"===e.title&&window.open("https://maps.google.com/?q=Via+Conca+d+Oro+323+Roma","_blank")},children:e.action})]})},t))})})}),(0,a.jsx)("section",{className:"py-16 bg-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"Inviaci un Messaggio"}),(0,a.jsx)("p",{className:"text-lg text-text-base opacity-80 mb-8",children:"Compila il form e ti contatteremo il prima possibile per rispondere alle tue domande o prenotare un appuntamento."}),"success"===c&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),(0,a.jsx)("span",{children:"Grazie per il tuo messaggio! Ti contatteremo presto."})]})}),"error"===c&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-red-500 mr-2",children:"✗"}),(0,a.jsx)("span",{children:m})]})}),(0,a.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"nome",className:"block text-sm font-medium text-text-base mb-2",children:"Nome *"}),(0,a.jsx)("input",{type:"text",id:"nome",name:"nome",value:e.nome,onChange:p,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Il tuo nome"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"telefono",className:"block text-sm font-medium text-text-base mb-2",children:"Telefono *"}),(0,a.jsx)("input",{type:"tel",id:"telefono",name:"telefono",value:e.telefono,onChange:p,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Il tuo numero"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-base mb-2",children:"Email *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:p,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"servizio",className:"block text-sm font-medium text-text-base mb-2",children:"Servizio di Interesse"}),(0,a.jsxs)("select",{id:"servizio",name:"servizio",value:e.servizio,onChange:p,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Seleziona un servizio"}),(0,a.jsx)("option",{value:"controllo-vista",children:"Controllo Vista"}),(0,a.jsx)("option",{value:"occhiali-vista",children:"Occhiali da Vista"}),(0,a.jsx)("option",{value:"occhiali-sole",children:"Occhiali da Sole"}),(0,a.jsx)("option",{value:"lenti-contatto",children:"Lenti a Contatto"}),(0,a.jsx)("option",{value:"riparazione",children:"Riparazione"}),(0,a.jsx)("option",{value:"altro",children:"Altro"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"messaggio",className:"block text-sm font-medium text-text-base mb-2",children:"Messaggio *"}),(0,a.jsx)("textarea",{id:"messaggio",name:"messaggio",value:e.messaggio,onChange:p,required:!0,rows:5,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical",placeholder:"Scrivi qui il tuo messaggio..."})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",id:"privacyConsent",name:"privacyConsent",checked:e.privacyConsent,onChange:p,required:!0,className:"mt-1 h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"}),(0,a.jsxs)("label",{htmlFor:"privacyConsent",className:"text-sm text-text-base leading-relaxed",children:["Accetto il trattamento dei dati personali secondo la"," ",(0,a.jsx)("a",{href:"/privacy",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-dark underline font-medium",children:"Privacy Policy"})," ","*"]})]}),(0,a.jsx)(r.A,{type:"submit",variant:"primary",size:"lg",fullWidth:!0,disabled:i,children:i?"Invio in corso...":"Invia Messaggio"})]})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold font-sans text-text-base mb-4",children:"Dove Siamo"}),(0,a.jsx)("div",{className:"bg-gray-200 rounded-lg h-64 flex items-center justify-center",children:(0,a.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2967.611162286211!2d12.5182373!3d41.944206099999995!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x132f66b2ed4f3b67%3A0xdbd9c055e4803223!2sOttica%20G.R.1%20S.r.l.!5e0!3m2!1sit!2sit!4v1752164767506!5m2!1sit!2sit",width:"800",height:"300",loading:"lazy"})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-4",children:"Come Raggiungerci"}),(0,a.jsxs)("div",{className:"space-y-3 text-text-base opacity-80",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE87"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Metro:"})," Linea B1 - Fermata Conca d'Oro"]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE8C"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Autobus:"})," Linee 38, 86, 90, 135"]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE97"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Auto:"})," Parcheggio convenzionato"]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83C\uDD7F️"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Parcheggio Convenzionato:"}),"  Roma Prime Garage -  Via Val Maira 6"]})]})]})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Vieni a Trovarci"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Il nostro team di esperti ti aspetta per offrirti il miglior servizio per la tua salute visiva."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(r.A,{variant:"accent",size:"lg",onClick:()=>{(0,n.trackButtonClick)("Prenota Controllo Vista","contatti_cta"),window.location.href="/esami-vista"},children:"Prenota Controllo Vista"}),(0,a.jsx)(r.A,{variant:"outline",size:"lg",onClick:()=>{(0,n.trackPhoneCall)("068862962","contatti_cta"),window.location.href="tel:068862962"},className:"border-white text-white hover:bg-white hover:text-primary",children:"Chiama: 068862962"})]})]})})]})}},418:(e,t,i)=>{"use strict";function a({title:e,description:t,keywords:i="",canonical:a,ogImage:s="/images/og-default.jpg",ogType:r="website",twitterCard:o="summary_large_image",noindex:n=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=a?`${l}${a}`:void 0,m=s.startsWith("http")?s:`${l}${s}`;return{title:c,description:t,keywords:i||void 0,robots:n?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:m,width:1200,height:630,alt:e}],locale:"it_IT",type:r},twitter:{card:o,title:c,description:t,images:[m],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>s,Y:()=>a});let s=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3533:(e,t,i)=>{Promise.resolve().then(i.bind(i,296)),Promise.resolve().then(i.bind(i,3121)),Promise.resolve().then(i.t.bind(i,5814,23))},3873:e=>{"use strict";e.exports=require("path")},4536:(e,t,i)=>{let{createProxy:a}=i(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5271:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},5917:(e,t,i)=>{Promise.resolve().then(i.bind(i,9374)),Promise.resolve().then(i.bind(i,5271)),Promise.resolve().then(i.t.bind(i,4536,23))},6985:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=i(5239),s=i(8088),r=i(8170),o=i.n(r),n=i(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(t,l);let c={children:["",{children:["contatti",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,9242)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\contatti\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\contatti\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/contatti/page",pathname:"/contatti",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9242:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l,metadata:()=>n});var a=i(7413),s=i(5271),r=i(273),o=i(9374);let n=(0,i(418).Y)({title:"Contatti - Prenota Visita e Informazioni Ottica GR1",description:"Contatta Ottica GR1 a Montesacro. Prenota controllo vista, richiedi informazioni su occhiali e lenti. Via Montesacro, Roma. Tel: 06 123 456 789.",keywords:"contatti ottica gr1, prenota controllo vista, ottica montesacro contatti, telefono ottica roma, indirizzo ottica montesacro",canonical:"/contatti"});function l(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsx)(o.default,{})}),(0,a.jsx)(r.A,{})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9374:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\contatti\\\\ContattiContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\contatti\\ContattiContent.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,988,926],()=>i(6985));module.exports=a})();