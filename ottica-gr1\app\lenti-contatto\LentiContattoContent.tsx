'use client';

import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import Carousel from '../../components/ui/Carousel';
import Button from '../../components/ui/Button';
import Image from 'next/image';

export default function LentiContattoContent() {
  const brands = [
    { id: '1', src: '/images/marchi/lenti-contatto/<PERSON>-logo.jpg', alt: '<PERSON> & Johnson' },
    { id: '2', src: '/images/marchi/lenti-contatto/CooperVision-logo.jpg', alt: 'CooperVision' },
    { id: '3', src: '/images/marchi/lenti-contatto/Bauschelomb-logo.jpg', alt: '<PERSON><PERSON><PERSON> + <PERSON><PERSON>' },
    { id: '4', src: '/images/marchi/lenti-contatto/Menicon-logo.jpg', alt: 'Menicon' },
    { id: '5', src: '/images/marchi/lenti-contatto/Soleko-logo.jpg', alt: 'Soleko' },
    { id: '6', src: '/images/marchi/lenti-contatto/Freeoptik-logo.jpg', alt: 'Freeoptik' },
  ];

  const contactLensManufacturers = [
    { name: 'Johnson & Johnson', description: 'Leader mondiale nelle lenti a contatto innovative' },
    { name: 'CooperVision', description: 'Tecnologia avanzata per comfort superiore' },
    { name: 'Bausch + Lomb', description: 'Oltre 160 anni di esperienza nella cura della vista' },
    { name: 'Alcon', description: 'Soluzioni complete per la salute degli occhi' },
    { name: 'Menicon', description: 'Innovazione giapponese per lenti di qualità' },
    { name: 'Soleko', description: 'Eccellenza italiana nelle lenti a contatto' },
    { name: 'Freeoptik', description: 'Soluzioni personalizzate per ogni esigenza' },
  ];

  const features = [
    {
      title: 'Lenti Giornaliere',
      description: 'Massima igiene e comfort con lenti usa e getta per ogni giorno della settimana.',
      icon: '📅'
    },
    {
      title: 'Lenti Mensili',
      description: 'Soluzione economica e pratica per chi usa le lenti regolarmente.',
      icon: '🗓️'
    },
    {
      title: 'Lenti Toriche',
      description: 'Correzione specializzata per astigmatismo con stabilità e nitidezza superiori.',
      icon: '👓'
    },
    {
      title: 'Lenti Multifocali',
      description: 'Visione perfetta a tutte le distanze per chi soffre di presbiopia.',
      icon: '🔍'
    },
    {
      title: 'Liquido pulizia lenti',
      description: 'Tieni pulite le lenti a contatto con la soluzione apposita.',
      icon: '​💧​'
    },
    {
      title: 'Consulenza Specializzata',
      description: 'I nostri esperti ti guidano nella scelta delle lenti più adatte a te.',
      icon: '👨‍⚕️'
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-accent text-lg font-sans font-medium mb-4">
                  Comfort e Libertà
                </p>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
                  Lenti a Contatto
                </h1>
                <p className="text-xl text-text-base opacity-80 leading-relaxed mb-8">
                  Scopri la libertà di movimento con le nostre lenti a contatto di ultima generazione. Offriamo una vasta gamma di soluzioni per ogni esigenza: giornaliere, mensili, toriche e multifocali dei migliori brand internazionali.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => window.location.href = '/contatti'}
                  >
                    Prova Gratuita Lenti
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = '/esami-vista'}
                  >
                    Controllo Vista
                  </Button>
                </div>
              </div>
              <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/section-cards/DSC09603.jpeg"
                  alt="Lenti a contatto di qualità"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                Tipologie di Lenti
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Soluzioni per ogni esigenza visiva e stile di vita
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center p-6 bg-background rounded-lg hover:shadow-lg transition-shadow duration-300">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold font-sans text-text-base mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-text-base opacity-80 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Lenti a Contatto Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-8 text-center">
                Le Nostre Lenti a Contatto e Soluzioni: Comfort e Chiarezza per Ogni Giorno
              </h2>
              
              <div className="prose prose-lg max-w-none text-text-base opacity-90 leading-relaxed space-y-6">
                <p>
                  Per chi desidera una libertà visiva senza compromessi, offriamo un'ampia gamma di lenti a contatto e le relative soluzioni per la manutenzione, selezionate tra i marchi più affidabili e innovativi del settore. Sappiamo quanto sia importante il comfort, la sicurezza e la chiarezza visiva per i portatori di lenti a contatto, per questo collaboriamo solo con aziende leader che garantiscono prodotti di altissima qualità.
                </p>
                
                <p>
                  Che tu cerchi lenti giornaliere, quindicinali o mensili, per miopia, ipermetropia, astigmatismo o presbiopia, e le soluzioni più adatte per la loro pulizia e conservazione, troverai la risposta alle tue esigenze. Il nostro team è a tua disposizione per consigliarti la soluzione migliore, assicurandoti il massimo benessere per i tuoi occhi.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Lens Manufacturers Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold font-sans text-text-base mb-6">
                I nostri marchi includono:
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
              {contactLensManufacturers.map((manufacturer, index) => (
                <div key={index} className="bg-background p-6 rounded-lg text-center hover:shadow-lg transition-shadow duration-300">
                  <h4 className="text-xl font-semibold font-sans text-primary mb-3">
                    {manufacturer.name}
                  </h4>
                  <p className="text-text-base opacity-80 text-sm">
                    {manufacturer.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Brands Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                I Nostri Partner
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Collaboriamo con i migliori marchi internazionali per offrirti lenti a contatto di qualità superiore
              </p>
            </div>
            
            {brands.length > 0 && (
              <div className="max-w-6xl mx-auto">
                <Carousel
                  items={brands}
                  autoPlay={true}
                  autoPlayInterval={3500}
                  showDots={true}
                  showArrows={true}
                  itemsPerView={4}
                  className="brands-carousel carousel-container"
                />
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
              Prova Gratuita Lenti a Contatto
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Scopri il comfort delle nostre lenti a contatto con una prova gratuita. 
              I nostri esperti ti guideranno nella scelta perfetta per te.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
              >
                Prenota Prova Gratuita
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = 'tel:068862962'}
                className="border-white text-white hover:bg-white hover:text-primary"
              >
                Chiama: 068862962
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
