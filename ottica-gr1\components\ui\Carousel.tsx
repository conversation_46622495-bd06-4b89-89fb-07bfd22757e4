'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import useResponsive from './useResponsive';

interface CarouselItem {
  id: string;
  src: string;
  alt: string;
  title?: string;
}

interface CarouselProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  itemsPerView?: number;
  className?: string;
}

const Carousel = ({
  items,
  autoPlay = true,
  autoPlayInterval = 3000,
  showDots = true,
  showArrows = true,
  itemsPerView = 1,
  className = '',
}: CarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { isMobile, isTablet } = useResponsive();

  // Responsive items per view
  const getResponsiveItemsPerView = () => {
    if (isMobile) return Math.min(2, itemsPerView);
    if (isTablet) return Math.min(3, itemsPerView);
    return itemsPerView;
  };

  const responsiveItemsPerView = getResponsiveItemsPerView();
  const totalSlides = Math.ceil(items.length / responsiveItemsPerView);

  // Auto-play functionality with smooth transitions
  const goToNext = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalSlides);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);
  }, [isTransitioning, totalSlides]);

  const goToPrevious = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalSlides) % totalSlides);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);
  }, [isTransitioning, totalSlides]);

  const goToSlide = useCallback((index: number) => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex(index);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);
  }, [isTransitioning]);

  useEffect(() => {
    if (autoPlay && !isHovered && totalSlides > 1 && !isTransitioning) {
      intervalRef.current = setInterval(() => {
        goToNext();
      }, autoPlayInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoPlay, autoPlayInterval, isHovered, totalSlides, isTransitioning, goToNext]);

  // Get visible items for current slide
  const getVisibleItems = () => {
    const startIdx = currentIndex * responsiveItemsPerView;
    const endIdx = startIdx + responsiveItemsPerView;
    return items.slice(startIdx, endIdx);
  };

  return (
    <div
      className={`relative w-full ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main carousel container */}
      <div className="relative overflow-hidden rounded-lg">
        <div
          className={`flex transition-all duration-700 ease-out transform ${
            isTransitioning ? 'scale-[0.98]' : 'scale-100'
          } ${isMobile ? 'duration-500' : 'duration-700'}`}
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            willChange: 'transform',
          }}
        >
          {Array.from({ length: totalSlides }).map((_, slideIndex) => (
            <div
              key={slideIndex}
              className="w-full flex-shrink-0"
            >
              <div className={`flex w-full ${responsiveItemsPerView > 1 ? 'gap-4 px-2' : ''}`}>
                {items
                  .slice(slideIndex * responsiveItemsPerView, (slideIndex + 1) * responsiveItemsPerView)
                  .map((item, itemIndex) => (
                    <div
                      key={item.id}
                      className={`${responsiveItemsPerView > 1 ? 'flex-1' : 'w-full'} relative group`}
                      style={{
                        animationDelay: `${itemIndex * 100}ms`,
                      }}
                    >
                      <div className="relative h-32 md:h-40 lg:h-48 w-full">
                        <div className="relative h-full w-full bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-500 p-4 flex items-center justify-center group-hover:scale-105 group-hover:bg-gray-50">
                          <Image
                            src={item.src}
                            alt={item.alt}
                            fill
                            className="object-contain transition-all duration-500 group-hover:scale-110 p-2 filter group-hover:brightness-110"
                          />
                        </div>
                        {item.title && (
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-3 rounded-b-lg">
                            <h3 className="text-sm font-medium text-center">{item.title}</h3>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      {showArrows && totalSlides > 1 && (
        <>
          <button
            onClick={goToPrevious}
            disabled={isTransitioning}
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-10 disabled:opacity-50 disabled:cursor-not-allowed group"
            aria-label="Previous slide"
          >
            <svg className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={goToNext}
            disabled={isTransitioning}
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-primary p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-10 disabled:opacity-50 disabled:cursor-not-allowed group"
            aria-label="Next slide"
          >
            <svg className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Dots indicator */}
      {showDots && totalSlides > 1 && (
        <div className="flex justify-center mt-8 space-x-3">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              disabled={isTransitioning}
              className={`relative transition-all duration-500 disabled:cursor-not-allowed ${
                index === currentIndex
                  ? 'w-8 h-3 bg-primary rounded-full'
                  : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full hover:scale-125'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            >
              {index === currentIndex && (
                <div className="absolute inset-0 bg-primary rounded-full animate-pulse opacity-50"></div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default Carousel;
