(()=>{var e={};e.id=632,e.ids=[632],e.modules={136:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var i=a(687),s=a(474);let o=({children:e,className:t="",image:a,imageAlt:o="",title:r,description:l,hover:n=!0,grayscaleHover:c=!1})=>{let d=`bg-white rounded-lg shadow-md overflow-hidden ${n?"transition-all duration-300 hover:shadow-xl hover:-translate-y-1":""} ${t}`.trim();return(0,i.jsxs)("div",{className:d,children:[a&&(0,i.jsx)("div",{className:"relative h-48 w-full overflow-hidden",children:(0,i.jsx)(s.default,{src:a,alt:o,fill:!0,className:`object-cover transition-all duration-300 ${c?"grayscale-hover":""}`})}),(r||l||e)&&(0,i.jsxs)("div",{className:"p-6",children:[r&&(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-2",children:r}),l&&(0,i.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed mb-4",children:l}),e]})]})}},273:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var i=a(7413),s=a(4536),o=a.n(s);let r=()=>{let e=new Date().getFullYear();return(0,i.jsx)("footer",{className:"bg-primary text-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,i.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,i.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,i.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,i.jsx)("li",{children:(0,i.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,i.jsx)("li",{children:(0,i.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,i.jsx)("li",{children:(0,i.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,i.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,i.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,i.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,i.jsx)("p",{children:"✉️ <EMAIL>"}),(0,i.jsxs)("div",{className:"pt-2",children:[(0,i.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,i.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,i.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,i.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,i.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,i.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,i.jsxs)("div",{className:"flex space-x-6",children:[(0,i.jsx)(o(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,i.jsx)(o(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},418:(e,t,a)=>{"use strict";function i({title:e,description:t,keywords:a="",canonical:i,ogImage:s="/images/og-default.jpg",ogType:o="website",twitterCard:r="summary_large_image",noindex:l=!1}){let n="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=i?`${n}${i}`:void 0,m=s.startsWith("http")?s:`${n}${s}`;return{title:c,description:t,keywords:a||void 0,robots:l?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:m,width:1200,height:630,alt:e}],locale:"it_IT",type:o},twitter:{card:r,title:c,description:t,images:[m],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}a.d(t,{U:()=>s,Y:()=>i});let s=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1469:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});let i=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\esami-vista\\\\EsamiVistaContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\esami-vista\\EsamiVistaContent.tsx","default")},1597:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var i=a(5239),s=a(8088),o=a(8170),r=a.n(o),l=a(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let c={children:["",{children:["esami-vista",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5035)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\esami-vista\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\esami-vista\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/esami-vista/page",pathname:"/esami-vista",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4220:(e,t,a)=>{Promise.resolve().then(a.bind(a,1469)),Promise.resolve().then(a.bind(a,5271)),Promise.resolve().then(a.t.bind(a,4536,23))},4536:(e,t,a)=>{let{createProxy:i}=a(9844);e.exports=i("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5035:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n,metadata:()=>l});var i=a(7413),s=a(5271),o=a(273),r=a(1469);let l=(0,a(418).Y)({title:"Esami della Vista - Controllo Professionale con Tecnologie Avanzate",description:"Esami della vista professionali a Montesacro. Autorefractometro, topografo corneale, forottero computerizzato. Prenotazione controllo vista gratuito.",keywords:"esami della vista roma, controllo vista montesacro, autorefractometro, topografo corneale, optometrista, misurazione vista, prenotazione",canonical:"/esami-vista"});function n(){return(0,i.jsxs)("div",{className:"min-h-screen",children:[(0,i.jsx)(s.default,{}),(0,i.jsx)("main",{className:"pt-20",children:(0,i.jsx)(r.default,{})}),(0,i.jsx)(o.A,{})]})}},5271:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});let i=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},6915:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var i=a(687),s=a(2294),o=a(136),r=a(474);function l(){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-accent text-lg font-sans font-medium mb-4",children:"Tecnologie Avanzate"}),(0,i.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:"Esami della Vista"}),(0,i.jsx)("p",{className:"text-xl text-text-base opacity-80 leading-relaxed mb-8",children:"Prenota un controllo della vista professionale con le nostre tecnologie diagnostiche di ultima generazione. I nostri esperti ti offriranno un servizio completo e personalizzato."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsx)(s.A,{variant:"primary",size:"lg",onClick:()=>window.location.href="/contatti",children:"Prenota Ora"}),(0,i.jsx)(s.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="tel:06123456789",children:"Chiama Ora"})]})]}),(0,i.jsx)("div",{className:"relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl",children:(0,i.jsx)(r.default,{src:"/images/slider/DSC09552.jpeg",alt:"Controllo vista professionale",fill:!0,className:"object-cover"})})]})})}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"I Nostri Servizi"}),(0,i.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Offriamo diversi tipi di controlli per soddisfare ogni esigenza"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Controllo Vista Completo",description:"Esame approfondito della vista con misurazione della refrazione e controllo della salute oculare.",duration:"30-45 minuti",price:"Gratuito*",features:["Misurazione vista","Controllo refrazione","Analisi binoculare","Consulenza personalizzata"]},{title:"Esame Computerizzato",description:"Tecnologia avanzata per una misurazione precisa e rapida dei difetti visivi.",duration:"20-30 minuti",price:"Incluso",features:["Autorefractometro","Tonometria Oculare","Pachimetria Corneale","Analisi digitale","Report dettagliato"]},{title:"Controllo Lenti a Contatto",description:"Valutazione specifica per l'applicazione e il controllo delle lenti a contatto.",duration:"45-60 minuti",price:"Su richiesta",features:["Test di compatibilit\xe0","Prova lenti","Istruzioni d'uso","Follow-up incluso"]}].map((e,t)=>(0,i.jsx)(o.A,{className:"h-full",children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-text-base opacity-80 mb-4",children:e.description}),(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4 text-sm",children:[(0,i.jsxs)("span",{className:"text-accent font-medium",children:["Durata: ",e.duration]}),(0,i.jsx)("span",{className:"text-primary font-bold",children:e.price})]}),(0,i.jsx)("ul",{className:"space-y-2 mb-6",children:e.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center text-sm text-text-base",children:[(0,i.jsx)("span",{className:"text-accent mr-2",children:"✓"}),e]},t))}),(0,i.jsx)(s.A,{variant:"outline",fullWidth:!0,onClick:()=>window.location.href="/contatti",children:"Prenota"})]})},t))}),(0,i.jsx)("div",{className:"text-center mt-8",children:(0,i.jsx)("p",{className:"text-sm text-text-base opacity-70",children:"* Gratuito con l'acquisto di occhiali o lenti a contatto"})})]})}),(0,i.jsx)("section",{className:"py-16 bg-background",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Tecnologie All'Avanguardia"}),(0,i.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Utilizziamo strumentazione professionale di ultima generazione per garantire la massima precisione nei nostri controlli"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Autorefractometro Digitale",description:"Misurazione automatica e precisa dei difetti refrattivi",icon:"\uD83D\uDD2C"},{name:"Tonometria Oculare",description:"Esame che misura la pressione oculare",icon:"\uD83D\uDC41️"},{name:"Pachimetria Corneale",description:"Misurazione dello spessore della cornea",icon:"\uD83D\uDCBB"},{name:"Biomicroscopio",description:"Esame dettagliato delle strutture oculari anteriori",icon:"\uD83D\uDD0D"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-lg font-semibold font-sans text-text-base mb-3",children:e.name}),(0,i.jsx)("p",{className:"text-text-base opacity-80 text-sm",children:e.description})]},t))})]})}),(0,i.jsxs)("section",{className:"py-16 bg-white",children:[(0,i.jsx)("div",{className:"text-center mb-12",children:(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Strumenti all'avanguardia per l'esame della vista"})}),(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsx)("div",{className:"relative w-full h-0 pb-[56.25%] rounded-lg overflow-hidden shadow-xl",children:(0,i.jsx)("iframe",{src:"https://player.vimeo.com/video/1100560874",className:"absolute top-0 left-0 w-full h-full",frameBorder:"0",allow:"autoplay; fullscreen; picture-in-picture",allowFullScreen:!0,title:"Controllo Vista Professionale"})})})]}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans text-text-base mb-4",children:"Come Funziona"}),(0,i.jsx)("p",{className:"text-lg text-text-base opacity-80 max-w-2xl mx-auto",children:"Il nostro processo di controllo vista in 4 semplici passaggi"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{step:"1",title:"Prenotazione",desc:"Prenota il tuo appuntamento online o per telefono"},{step:"2",title:"Accoglienza",desc:"Ti accogliamo e raccogliamo la tua storia visiva"},{step:"3",title:"Esame",desc:"Eseguiamo il controllo con strumentazione avanzata"},{step:"4",title:"Consulenza",desc:"Ti consigliamo la soluzione migliore per te"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold font-sans mx-auto mb-4",children:e.step}),(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-text-base opacity-80",children:e.desc})]},t))})]})}),(0,i.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Prenota il Tuo Controllo Vista"}),(0,i.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Non aspettare, la tua vista \xe8 importante. Prenota oggi stesso un controllo professionale con i nostri esperti."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(s.A,{variant:"accent",size:"lg",onClick:()=>window.location.href="/contatti",children:"Prenota Online"}),(0,i.jsx)(s.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="tel:068862962",className:"border-white text-white hover:bg-white hover:text-primary",children:"Chiama: 068862962"})]})]})})]})}},8687:(e,t,a)=>{Promise.resolve().then(a.bind(a,6915)),Promise.resolve().then(a.bind(a,3121)),Promise.resolve().then(a.t.bind(a,5814,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[447,988,926],()=>a(1597));module.exports=i})();