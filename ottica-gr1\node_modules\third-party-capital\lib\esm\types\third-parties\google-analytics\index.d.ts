import type { Inputs } from '../../types';
export declare const GoogleAnalytics: ({ ...args }: Inputs) => {
    html: string | null;
    scripts: {
        url: string;
        params?: string[] | undefined;
        strategy: "server" | "client" | "idle" | "worker";
        location: "head" | "body";
        action: "append" | "prepend";
    }[] | null;
    id: string;
    description: string;
    website?: string | undefined;
    stylesheets?: string[] | undefined;
};
//# sourceMappingURL=index.d.ts.map