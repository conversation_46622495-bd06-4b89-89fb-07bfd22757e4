import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { nome, email, telefono, servizio, messaggio, privacyConsent } = body;

    // Validazione dei dati
    if (!nome || !email || !telefono || !messaggio) {
      return NextResponse.json(
        { error: 'Nome, email, telefono e messaggio sono obbligatori' },
        { status: 400 }
      );
    }

    // Validazione del consenso privacy
    if (!privacyConsent) {
      return NextResponse.json(
        { error: 'È necessario accettare il trattamento dei dati personali per inviare il messaggio' },
        { status: 400 }
      );
    }

    // Configurazione del trasportatore email
    // NOTA: Queste variabili d'ambiente devono essere configurate
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true per 465, false per altri port
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Contenuto dell'email
    const emailContent = `
      <h2>Nuovo messaggio dal sito web Ottica GR1</h2>
      <p><strong>Nome:</strong> ${nome}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Telefono:</strong> ${telefono || 'Non fornito'}</p>
      <p><strong>Servizio richiesto:</strong> ${servizio || 'Non specificato'}</p>
      <p><strong>Messaggio:</strong></p>
      <p>${messaggio.replace(/\n/g, '<br>')}</p>
      
      <hr>
      <p><small>Questo messaggio è stato inviato dal form di contatto del sito web Ottica GR1.</small></p>
    `;

    // Opzioni dell'email
    const mailOptions = {
      from: `"${nome}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: process.env.SMTP_TO || process.env.SMTP_USER,
      subject: `Nuovo messaggio da ${nome} - Ottica GR1`,
      html: emailContent,
      replyTo: email,
    };

    // Invio dell'email
    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { message: 'Email inviata con successo' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Errore nell\'invio dell\'email:', error);
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}
