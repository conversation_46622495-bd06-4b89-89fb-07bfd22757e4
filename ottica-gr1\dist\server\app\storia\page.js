(()=>{var e={};e.id=7,e.ids=[7],e.modules={273:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var a=i(7413),s=i(4536),o=i.n(s);let r=()=>{let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,a.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,a.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,a.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,a.jsx)("p",{children:"✉️ <EMAIL>"}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,a.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(o(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,a.jsx)(o(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},418:(e,t,i)=>{"use strict";function a({title:e,description:t,keywords:i="",canonical:a,ogImage:s="/images/og-default.jpg",ogType:o="website",twitterCard:r="summary_large_image",noindex:n=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=a?`${l}${a}`:void 0,m=s.startsWith("http")?s:`${l}${s}`;return{title:c,description:t,keywords:i||void 0,robots:n?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:m,width:1200,height:630,alt:e}],locale:"it_IT",type:o},twitter:{card:r,title:c,description:t,images:[m],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>s,Y:()=>a});let s=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},603:(e,t,i)=>{Promise.resolve().then(i.bind(i,5271)),Promise.resolve().then(i.t.bind(i,4536,23)),Promise.resolve().then(i.t.bind(i,9603,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},1322:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:a,blurHeight:s,blurDataURL:o,objectFit:r}=e,n=a?40*a:t,l=s?40*s:i,c=n&&l?"viewBox='0 0 "+n+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===r?"xMidYMid":"cover"===r?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},2091:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:a,width:s,quality:o}=e,r=o||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(a)+"&w="+s+"&q="+r+(a.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),i.__next_img_default=!0;let a=i},2480:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return n}});let a=i(2639),s=i(9131),o=i(9603),r=a._(i(2091));function n(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=o.Image},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3747:(e,t,i)=>{Promise.resolve().then(i.bind(i,3121)),Promise.resolve().then(i.t.bind(i,5814,23)),Promise.resolve().then(i.t.bind(i,6533,23))},3761:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=i(5239),s=i(8088),o=i(8170),r=i.n(o),n=i(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(t,l);let c={children:["",{children:["storia",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,7144)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\storia\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\storia\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/storia/page",pathname:"/storia",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3873:e=>{"use strict";e.exports=require("path")},4536:(e,t,i)=>{let{createProxy:a}=i(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5271:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},7144:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c,metadata:()=>l});var a=i(7413),s=i(5271),o=i(273),r=i(2480),n=i.n(r);let l=(0,i(418).Y)({title:"La Nostra Storia - Tradizione e Innovazione dal 1982",description:"Scopri la storia di Ottica GR1, dal 1982 a Montesacro. Tradizione familiare, innovazione e cura del cliente per oltre 40 anni. Due generazioni al servizio della vista.",keywords:"storia ottica gr1, ottica montesacro dal 1982, tradizione familiare ottica, esperienza ottica roma, storia negozio occhiali, occhiali da sole roma",canonical:"/storia"});function c(){let e=[{src:"/images/storia/sto.jpeg",alt:"Ottica GR1 - Interno del negozio"},{src:"/images/storia/storia.jpeg",alt:"Ottica GR1 - Esposizione occhiali"},{src:"/images/storia/storia3.jpeg",alt:"Ottica GR1 - Area consulenza"},{src:"/images/storia/storia-2.jpeg",alt:"Ottica GR1 - Strumentazione professionale"}];return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{}),(0,a.jsxs)("main",{className:"pt-20",children:[(0,a.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:["La Storia di ",(0,a.jsx)("span",{className:"text-primary",children:"Ottica GR1"})]}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-text-base opacity-80 leading-relaxed",children:"Tradizione, Innovazione e Cura del Cliente"})]})})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"Le Nostre Origini"}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["Fondata nel ",(0,a.jsx)("strong",{children:"1982"})," nel cuore pulsante del quartiere ",(0,a.jsx)("strong",{children:"Montesacro"}),", Ottica GR1 ha radici profonde in un'impresa a carattere familiare. Nata dalla passione e dalla dedizione dei suoi fondatori, l'attivit\xe0 si \xe8 rapidamente affermata come un punto di riferimento indiscusso per la comunit\xe0 locale."]}),(0,a.jsx)("p",{className:"text-lg leading-relaxed text-text-base",children:"Fin dai primi anni, l'impegno costante nella ricerca della qualit\xe0 superiore e l'attenzione meticolosa alla cortesia nel servizio offerto hanno costituito i pilastri fondamentali della sua reputazione. Ottica GR1 si \xe8 distinta per la vasta gamma di occhiali da vista e da sole, selezionati con cura per garantire non solo performance visive eccellenti, ma anche stili che anticipavano e soddisfacevano le esigenze della clientela."})]}),(0,a.jsx)("div",{className:"relative h-96 rounded-lg overflow-hidden shadow-xl",children:(0,a.jsx)(n(),{src:e[0].src,alt:e[0].alt,fill:!0,className:"object-cover"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16",children:[(0,a.jsx)("div",{className:"relative h-96 rounded-lg overflow-hidden shadow-xl lg:order-1",children:(0,a.jsx)(n(),{src:e[1].src,alt:e[1].alt,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"space-y-6 lg:order-2",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"La Seconda Generazione"}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["Oggi, a oltre ",(0,a.jsx)("strong",{children:"quarant'anni"})," dalla sua fondazione, la tradizione e i valori originali di Ottica GR1 sono splendidamente portati avanti dalla ",(0,a.jsx)("strong",{children:"seconda generazione"}),"della famiglia. I figli, eredi di un prezioso patrimonio di esperienza, hanno saputo integrare con maestria lo spirito autentico dell'azienda con le innovazioni pi\xf9 all'avanguardia nel campo dell'ottica."]}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["Questo si traduce nell'impiego delle nuove ",(0,a.jsx)("strong",{children:"tecnologie diagnostiche e di misurazione"}),", che consentono un'analisi ancora pi\xf9 precisa e personalizzata delle esigenze visive di ogni cliente."]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"Collaborazioni Professionali"}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["La collaborazione con ",(0,a.jsx)("strong",{children:"oculisti di grande professionalit\xe0 ed esperienza"})," \xe8 un altro elemento distintivo che ha elevato ulteriormente gli standard di Ottica GR1. Questa sinergia tra la competenza ottica e la consulenza medica specialistica garantisce un approccio olistico al benessere visivo."]}),(0,a.jsx)("p",{className:"text-lg leading-relaxed text-text-base",children:"L'impegno costante nel mantenimento di un elevato livello di servizio e l'attenzione alle relazioni interpersonali hanno permesso non solo di conservare, ma di rafforzare ulteriormente il rapporto di fiducia con la clientela. La soddisfazione e la fedelt\xe0 dei clienti nel corso degli anni testimoniano l'efficacia di questa filosofia."})]}),(0,a.jsx)("div",{className:"relative h-96 rounded-lg overflow-hidden shadow-xl",children:(0,a.jsx)(n(),{src:e[2].src,alt:e[2].alt,fill:!0,className:"object-cover"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsx)("div",{className:"relative h-96 rounded-lg overflow-hidden shadow-xl lg:order-1",children:(0,a.jsx)(n(),{src:e[3].src,alt:e[3].alt,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"space-y-6 lg:order-2",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"Il Nostro Futuro"}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["Ottica GR1 continua a evolversi, affermandosi non solo come un negozio di ottica, ma come un autentico ",(0,a.jsx)("strong",{children:"punto di riferimento per la salute e il benessere degli occhi"})," a Montesacro e oltre."]}),(0,a.jsxs)("p",{className:"text-lg leading-relaxed text-text-base",children:["L'obiettivo primario rimane quello di offrire ",(0,a.jsx)("strong",{children:"soluzioni visive ottimali"}),", unite a un'esperienza d'acquisto impareggiabile, dove professionalit\xe0, innovazione e calore umano si fondono armoniosamente per il massimo beneficio del cliente."]})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Vieni a Trovarci"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Scopri di persona la nostra esperienza e professionalit\xe0. Ti aspettiamo nel nostro negozio a Montesacro."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"/contatti",className:"bg-accent hover:bg-accent-light text-white font-sans font-medium px-8 py-3 rounded-lg transition-colors duration-300",children:"Contattaci"}),(0,a.jsx)("a",{href:"/esami-vista",className:"bg-transparent border-2 border-white hover:bg-white hover:text-primary text-white font-sans font-medium px-8 py-3 rounded-lg transition-colors duration-300",children:"Prenota Controllo Vista"})]})]})})]}),(0,a.jsx)(o.A,{})]})}},7894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return a}});let i=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(1122);let a=i(1322),s=i(7894),o=["-moz-initial","fill","none","scale-down",void 0];function r(e){return void 0!==e.default}function n(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let c,d,m,{src:p,sizes:u,unoptimized:h=!1,priority:g=!1,loading:x,className:f,quality:v,width:j,height:b,fill:y=!1,style:N,overrideSrc:z,onLoad:w,onLoadingComplete:O,placeholder:P="empty",blurDataURL:_,fetchPriority:C,decoding:R="async",layout:S,objectFit:E,objectPosition:D,lazyBoundary:k,lazyRoot:M,...G}=e,{imgConf:I,showAltText:A,blurComplete:L,defaultLoader:T}=t,q=I||s.imageConfigDefault;if("allSizes"in q)c=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),a=null==(i=q.qualities)?void 0:i.sort((e,t)=>e-t);c={...q,allSizes:e,deviceSizes:t,qualities:a}}if(void 0===T)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let V=G.loader||T;delete G.loader,delete G.srcSet;let F="__next_img_default"in V;if(F){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=V;V=t=>{let{config:i,...a}=t;return e(a)}}if(S){"fill"===S&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(N={...N,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!u&&(u=t)}let U="",W=n(j),B=n(b);if((l=p)&&"object"==typeof l&&(r(l)||void 0!==l.src)){let e=r(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,m=e.blurHeight,_=_||e.blurDataURL,U=e.src,!y)if(W||B){if(W&&!B){let t=W/e.width;B=Math.round(e.height*t)}else if(!W&&B){let t=B/e.height;W=Math.round(e.width*t)}}else W=e.width,B=e.height}let H=!g&&("lazy"===x||void 0===x);(!(p="string"==typeof p?p:U)||p.startsWith("data:")||p.startsWith("blob:"))&&(h=!0,H=!1),c.unoptimized&&(h=!0),F&&!c.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(h=!0);let $=n(v),X=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:D}:{},A?{}:{color:"transparent"},N),Y=L||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:W,heightInt:B,blurWidth:d,blurHeight:m,blurDataURL:_||"",objectFit:X.objectFit})+'")':'url("'+P+'")',J=o.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Q=Y?{backgroundSize:J,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},K=function(e){let{config:t,src:i,unoptimized:a,width:s,quality:o,sizes:r,loader:n}=e;if(a)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,i){let{deviceSizes:a,allSizes:s}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(i);)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,r),d=l.length-1;return{sizes:r||"w"!==c?r:"100vw",srcSet:l.map((e,a)=>n({config:t,src:i,quality:o,width:e})+" "+("w"===c?e:a+1)+c).join(", "),src:n({config:t,src:i,quality:o,width:l[d]})}}({config:c,src:p,unoptimized:h,width:W,quality:$,sizes:u,loader:V});return{props:{...G,loading:H?"lazy":x,fetchPriority:C,width:W,height:B,decoding:R,className:f,style:{...X,...Q},sizes:K.sizes,srcSet:K.srcSet,src:z||K.src},meta:{unoptimized:h,priority:g,placeholder:P,fill:y}}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9603:(e,t,i)=>{let{createProxy:a}=i(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\image-component.js")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,988,926],()=>i(3761));module.exports=a})();