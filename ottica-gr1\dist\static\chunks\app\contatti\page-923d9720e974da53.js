(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{1202:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var i=a(5155),s=a(6766);let r=e=>{let{children:t,className:a="",image:r,imageAlt:n="",title:o,description:l,hover:c=!0,grayscaleHover:d=!1}=e,m="".concat("bg-white rounded-lg shadow-md overflow-hidden"," ").concat(c?"transition-all duration-300 hover:shadow-xl hover:-translate-y-1":""," ").concat(a).trim();return(0,i.jsxs)("div",{className:m,children:[r&&(0,i.jsx)("div",{className:"relative h-48 w-full overflow-hidden",children:(0,i.jsx)(s.default,{src:r,alt:n,fill:!0,className:"object-cover transition-all duration-300 ".concat(d?"grayscale-hover":"")})}),(o||l||t)&&(0,i.jsxs)("div",{className:"p-6",children:[o&&(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-2",children:o}),l&&(0,i.jsx)("p",{className:"text-text-base opacity-80 leading-relaxed mb-4",children:l}),t]})]})}},1514:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GA_MEASUREMENT_ID:()=>i,initClickTracking:()=>w,initGA:()=>s,initScrollTracking:()=>y,trackButtonClick:()=>l,trackConversion:()=>j,trackEmailClick:()=>f,trackEngagement:()=>v,trackEvent:()=>o,trackFileDownload:()=>m,trackFormSubmission:()=>d,trackLinkClick:()=>c,trackPageLoadTime:()=>k,trackPageView:()=>n,trackPhoneCall:()=>u,trackPurchase:()=>b,trackScrollDepth:()=>h,trackSearch:()=>g,trackVideoInteraction:()=>x,trackWhatsAppClick:()=>p,updateConsent:()=>r});let i=a(9509).env.NEXT_PUBLIC_GA_MEASUREMENT_ID||"",s=()=>{i&&(window.dataLayer=window.dataLayer||[],window.gtag=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];window.dataLayer.push(t)},window.gtag("consent","default",{analytics_storage:"denied",ad_storage:"denied",ad_user_data:"denied",ad_personalization:"denied",wait_for_update:500}),window.gtag("js",new Date),window.gtag("config",i,{page_title:document.title,page_location:window.location.href}))},r=(e,t)=>{window.gtag&&window.gtag("consent","update",{analytics_storage:e?"granted":"denied",ad_storage:t?"granted":"denied",ad_user_data:t?"granted":"denied",ad_personalization:t?"granted":"denied"})},n=(e,t)=>{window.gtag&&window.gtag("config",i,{page_path:e,page_title:t||document.title})},o=(e,t,a,i,s)=>{window.gtag&&window.gtag("event",e,{event_category:t,event_label:a,value:i,...s})},l=(e,t)=>{o("click","button","".concat(e," - ").concat(t))},c=(e,t,a)=>{o("click","link","".concat(e," - ").concat(a),void 0,{link_url:t})},d=(e,t)=>{o("form_submit","form",e,void 0,{success:t})},m=(e,t)=>{o("file_download","download",e,void 0,{file_type:t})},x=(e,t,a)=>{o(e,"video",t,a)},h=e=>{o("scroll","engagement","".concat(e,"%"),e)},g=(e,t)=>{o("search","engagement",e,t)},u=(e,t)=>{o("phone_call","contact","".concat(e," - ").concat(t))},p=e=>{o("whatsapp_click","contact",e)},f=e=>{o("email_click","contact",e)},b=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"EUR";o("purchase","ecommerce",e,t,{transaction_id:e,currency:a})},v=e=>{o("user_engagement","engagement","time_on_page",e)},j=(e,t)=>{o("conversion","goal",e,t)},y=()=>{let e=[25,50,75,90,100],t=[],a=()=>{let a=Math.round((window.pageYOffset||document.documentElement.scrollTop)/(document.documentElement.scrollHeight-window.innerHeight)*100);e.forEach(e=>{a>=e&&!t.includes(e)&&(t.push(e),h(e))})};return window.addEventListener("scroll",a,{passive:!0}),()=>{window.removeEventListener("scroll",a)}},w=()=>{let e=e=>{var t,a;let i=e.target;if("BUTTON"===i.tagName||i.closest("button")){let e="BUTTON"===i.tagName?i:i.closest("button");l((null==e||null==(t=e.textContent)?void 0:t.trim())||"Unknown Button",N(e))}if("A"===i.tagName||i.closest("a")){let e="A"===i.tagName?i:i.closest("a"),t=(null==e||null==(a=e.textContent)?void 0:a.trim())||"Unknown Link",s=(null==e?void 0:e.href)||"",r=N(e);s&&!s.includes(window.location.hostname)?o("click","external_link","".concat(t," - ").concat(s),void 0,{link_url:s,location:r}):c(t,s,r)}};return document.addEventListener("click",e),()=>{document.removeEventListener("click",e)}},N=e=>{if(!e)return"unknown";let t=e.closest("section"),a=e.closest("header"),i=e.closest("footer"),s=e.closest("nav");if(a)return"header";if(i)return"footer";if(s)return"navigation";if(t){let e=t.className,a=t.id;return a||e&&e.split(" ")[0]||"section"}return"page"},k=()=>{window.addEventListener("load",()=>{setTimeout(()=>{let e=performance.getEntriesByType("navigation")[0];e&&o("page_load_time","performance","load_time",Math.round(e.loadEventEnd-e.fetchStart))},0)})}},3343:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var i=a(5155),s=a(2115),r=a(6874),n=a.n(r),o=a(6766),l=a(5695);let c=()=>{let[e,t]=(0,s.useState)(!1),[a,r]=(0,s.useState)(!1),c=(0,l.usePathname)();(0,s.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,s.useEffect)(()=>(a?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[a]);let d=[{href:"/",label:"HOME"},{href:"/storia",label:"STORIA"},{href:"/servizi",label:"SERVIZI"},{href:"/occhiali",label:"OCCHIALI DA VISTA"},{href:"/lenti-contatto",label:"LENTI A CONTATTO"},{href:"/occhiali-sole",label:"OCCHIALI DA SOLE"},{href:"/esami-vista",label:"ESAMI DELLA VISTA"},{href:"/contatti",label:"CONTATTI"}];return(0,i.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(e?"bg-white shadow-lg":"/"===c||a?"bg-white shadow-sm":"bg-white/90 shadow-sm"),children:(0,i.jsxs)("nav",{className:"container mx-auto px-4 py-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(n(),{href:"/",className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"relative h-10 w-10 flex-shrink-0",children:(0,i.jsx)(o.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain",priority:!0})}),(0,i.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,i.jsx)("span",{className:"text-primary transition-colors duration-300",children:"OTTICA"}),(0,i.jsx)("span",{className:"text-accent transition-colors duration-300 ml-1",children:"GR1"})]})]}),(0,i.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:d.map(e=>(0,i.jsx)(n(),{href:e.href,className:"font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ".concat(c===e.href?"text-primary":"text-text-base hover:text-primary"),children:e.label},e.href))}),(0,i.jsxs)("button",{className:"lg:hidden flex flex-col space-y-1 w-6 h-6",onClick:()=>r(!a),"aria-label":"Toggle mobile menu",children:[(0,i.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(a?"rotate-45 translate-y-1.5":"")}),(0,i.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(a?"opacity-0":"")}),(0,i.jsx)("span",{className:"block h-0.5 w-6 transition-all duration-300 bg-primary ".concat(a?"-rotate-45 -translate-y-1.5":"")})]})]}),a&&(0,i.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]",onClick:()=>r(!1)}),(0,i.jsx)("div",{className:"lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ".concat(a?"translate-x-0":"translate-x-full"),style:{backgroundColor:"#ffffff",backdropFilter:"none"},children:(0,i.jsxs)("div",{className:"flex flex-col h-full",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"relative h-8 w-8 flex-shrink-0",children:(0,i.jsx)(o.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain"})}),(0,i.jsxs)("div",{className:"text-lg font-bold font-sans",children:[(0,i.jsx)("span",{className:"text-primary",children:"OTTICA"}),(0,i.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]})]}),(0,i.jsx)("button",{onClick:()=>r(!1),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Close menu",children:(0,i.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,i.jsx)("nav",{className:"flex-1 px-6 py-6",children:(0,i.jsx)("div",{className:"space-y-2",children:d.map(e=>(0,i.jsx)(n(),{href:e.href,className:"block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ".concat(c===e.href?"text-primary bg-primary/10 border-l-4 border-primary":"text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1"),onClick:()=>r(!1),children:e.label},e.href))})}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Dal 1982 a Montesacro"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Tradizione e innovazione"})]})})]})})]})})}},4219:(e,t,a)=>{Promise.resolve().then(a.bind(a,8382)),Promise.resolve().then(a.bind(a,3343)),Promise.resolve().then(a.t.bind(a,6874,23))},4912:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var i=a(5155);let s=e=>{let{children:t,variant:a="primary",size:s="md",fullWidth:r=!1,className:n="",...o}=e,l="".concat("font-sans font-medium transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({primary:"bg-primary text-white hover:bg-primary-dark focus:ring-primary",secondary:"bg-gray-200 text-text-base hover:bg-gray-300 focus:ring-gray-400",accent:"bg-accent text-white hover:bg-accent-light focus:ring-accent",outline:"border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary"}[a]," ").concat({sm:"px-3 py-1.5 text-sm",md:"px-6 py-2.5 text-base",lg:"px-8 py-3 text-lg"}[s]," ").concat(r?"w-full":""," ").concat(n).trim();return(0,i.jsx)("button",{className:l,...o,children:t})}},8382:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var i=a(5155),s=a(2115),r=a(4912),n=a(1202),o=a(1514);function l(){let[e,t]=(0,s.useState)({nome:"",email:"",telefono:"",servizio:"",messaggio:"",privacyConsent:!1}),[a,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)("idle"),[m,x]=(0,s.useState)(""),h=e=>{let a=e.target,{name:i,value:s,type:r}=a,n="checked"in a&&a.checked;t(e=>({...e,[i]:"checkbox"===r?n:s}))},g=async a=>{if(a.preventDefault(),l(!0),d("idle"),x(""),!e.privacyConsent){d("error"),x("\xc8 necessario accettare il trattamento dei dati personali per inviare il messaggio."),l(!1);return}try{let a=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),i=await a.json();a.ok?(d("success"),(0,o.trackFormSubmission)("contact_form",!0),t({nome:"",email:"",telefono:"",servizio:"",messaggio:"",privacyConsent:!1})):(d("error"),(0,o.trackFormSubmission)("contact_form",!1),x(i.error||"Si \xe8 verificato un errore durante l'invio del messaggio."))}catch(e){d("error"),(0,o.trackFormSubmission)("contact_form",!1),x("Errore di connessione. Riprova pi\xf9 tardi.")}finally{l(!1)}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6",children:"Contattaci"}),(0,i.jsx)("p",{className:"text-xl md:text-2xl text-text-base opacity-80 leading-relaxed",children:"Siamo qui per aiutarti. Vieni a trovarci o contattaci per qualsiasi informazione."})]})})}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:[{icon:"\uD83D\uDCCD",title:"Indirizzo",details:["Via Conca D Oro, 323","00141 Roma (RM)"],action:"Vedi su Maps"},{icon:"\uD83D\uDCDE",title:"Telefono",details:["06 8862962","+39 3928480621  "],action:"Chiama ora"},{icon:"\uD83C\uDD7F️",title:"Parcheggio Convenzionato",details:["Via Val Maira 6","00141 Roma (RM)"],action:"Vedi su Maps"},{icon:"\uD83D\uDD52",title:"Orari di Apertura",details:["Lun: 16:00 - 19:30","Mar - Sab: 9:00 - 13:00","16:30 - 19:30","Dom: Chiuso"],action:null}].map((e,t)=>(0,i.jsx)(n.A,{className:"text-center h-full",children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-4",children:e.title}),(0,i.jsx)("div",{className:"space-y-1 mb-4",children:e.details.map((e,t)=>(0,i.jsx)("p",{className:"text-text-base opacity-80",children:e},t))}),e.action&&(0,i.jsx)(r.A,{variant:"outline",size:"sm",onClick:()=>{"Telefono"===e.title?window.location.href="tel:068862962":"Parcheggio Convenzionato"===e.title?window.open("https://maps.google.com/?q=Via+Val+Maira+6+Roma","_blank"):"Indirizzo"===e.title&&window.open("https://maps.google.com/?q=Via+Conca+d+Oro+323+Roma","_blank")},children:e.action})]})},t))})})}),(0,i.jsx)("section",{className:"py-16 bg-background",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-3xl font-bold font-sans text-text-base mb-6",children:"Inviaci un Messaggio"}),(0,i.jsx)("p",{className:"text-lg text-text-base opacity-80 mb-8",children:"Compila il form e ti contatteremo il prima possibile per rispondere alle tue domande o prenotare un appuntamento."}),"success"===c&&(0,i.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),(0,i.jsx)("span",{children:"Grazie per il tuo messaggio! Ti contatteremo presto."})]})}),"error"===c&&(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-red-500 mr-2",children:"✗"}),(0,i.jsx)("span",{children:m})]})}),(0,i.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"nome",className:"block text-sm font-medium text-text-base mb-2",children:"Nome *"}),(0,i.jsx)("input",{type:"text",id:"nome",name:"nome",value:e.nome,onChange:h,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Il tuo nome"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"telefono",className:"block text-sm font-medium text-text-base mb-2",children:"Telefono *"}),(0,i.jsx)("input",{type:"tel",id:"telefono",name:"telefono",value:e.telefono,onChange:h,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Il tuo numero"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-base mb-2",children:"Email *"}),(0,i.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:h,required:!0,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"servizio",className:"block text-sm font-medium text-text-base mb-2",children:"Servizio di Interesse"}),(0,i.jsxs)("select",{id:"servizio",name:"servizio",value:e.servizio,onChange:h,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,i.jsx)("option",{value:"",children:"Seleziona un servizio"}),(0,i.jsx)("option",{value:"controllo-vista",children:"Controllo Vista"}),(0,i.jsx)("option",{value:"occhiali-vista",children:"Occhiali da Vista"}),(0,i.jsx)("option",{value:"occhiali-sole",children:"Occhiali da Sole"}),(0,i.jsx)("option",{value:"lenti-contatto",children:"Lenti a Contatto"}),(0,i.jsx)("option",{value:"riparazione",children:"Riparazione"}),(0,i.jsx)("option",{value:"altro",children:"Altro"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"messaggio",className:"block text-sm font-medium text-text-base mb-2",children:"Messaggio *"}),(0,i.jsx)("textarea",{id:"messaggio",name:"messaggio",value:e.messaggio,onChange:h,required:!0,rows:5,className:"w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical",placeholder:"Scrivi qui il tuo messaggio..."})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("input",{type:"checkbox",id:"privacyConsent",name:"privacyConsent",checked:e.privacyConsent,onChange:h,required:!0,className:"mt-1 h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"}),(0,i.jsxs)("label",{htmlFor:"privacyConsent",className:"text-sm text-text-base leading-relaxed",children:["Accetto il trattamento dei dati personali secondo la"," ",(0,i.jsx)("a",{href:"/privacy",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-dark underline font-medium",children:"Privacy Policy"})," ","*"]})]}),(0,i.jsx)(r.A,{type:"submit",variant:"primary",size:"lg",fullWidth:!0,disabled:a,children:a?"Invio in corso...":"Invia Messaggio"})]})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold font-sans text-text-base mb-4",children:"Dove Siamo"}),(0,i.jsx)("div",{className:"bg-gray-200 rounded-lg h-64 flex items-center justify-center",children:(0,i.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2967.611162286211!2d12.5182373!3d41.944206099999995!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x132f66b2ed4f3b67%3A0xdbd9c055e4803223!2sOttica%20G.R.1%20S.r.l.!5e0!3m2!1sit!2sit!4v1752164767506!5m2!1sit!2sit",width:"800",height:"300",loading:"lazy"})})]}),(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold font-sans text-text-base mb-4",children:"Come Raggiungerci"}),(0,i.jsxs)("div",{className:"space-y-3 text-text-base opacity-80",children:[(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE87"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metro:"})," Linea B1 - Fermata Conca d'Oro"]})]}),(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE8C"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Autobus:"})," Linee 38, 86, 90, 135"]})]}),(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83D\uDE97"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Auto:"})," Parcheggio convenzionato"]})]}),(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)("span",{className:"text-accent mr-2 mt-1",children:"\uD83C\uDD7F️"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Parcheggio Convenzionato:"}),"  Roma Prime Garage -  Via Val Maira 6"]})]})]})]})]})]})})}),(0,i.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold font-sans mb-6",children:"Vieni a Trovarci"}),(0,i.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Il nostro team di esperti ti aspetta per offrirti il miglior servizio per la tua salute visiva."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(r.A,{variant:"accent",size:"lg",onClick:()=>{(0,o.trackButtonClick)("Prenota Controllo Vista","contatti_cta"),window.location.href="/esami-vista"},children:"Prenota Controllo Vista"}),(0,i.jsx)(r.A,{variant:"outline",size:"lg",onClick:()=>{(0,o.trackPhoneCall)("068862962","contatti_cta"),window.location.href="tel:068862962"},className:"border-white text-white hover:bg-white hover:text-primary",children:"Chiama: 068862962"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[261,441,684,358],()=>t(4219)),_N_E=e.O()}]);