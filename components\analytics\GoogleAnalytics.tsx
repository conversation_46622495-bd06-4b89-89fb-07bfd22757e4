'use client';

import { useEffect } from 'react';
import Script from 'next/script';
import { initGA, initScrollTracking, initClickTracking, trackPageLoadTime } from '../../lib/analytics';

interface GoogleAnalyticsProps {
  measurementId: string;
}

export default function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  useEffect(() => {
    // Initialize GA when the script loads
    const handleScriptLoad = () => {
      initGA();
      
      // Initialize tracking utilities
      const cleanupScroll = initScrollTracking();
      const cleanupClick = initClickTracking();
      trackPageLoadTime();
      
      // Cleanup function
      return () => {
        if (cleanupScroll) cleanupScroll();
        if (cleanupClick) cleanupClick();
      };
    };

    // If gtag is already available, initialize immediately
    if (typeof window !== 'undefined' && window.gtag) {
      handleScriptLoad();
    }

    return () => {
      // Cleanup will be handled by the individual tracking functions
    };
  }, []);

  if (!measurementId) {
    return null;
  }

  return (
    <>
      {/* Google Analytics Script */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
        onLoad={() => {
          initGA();
          initScrollTracking();
          initClickTracking();
          trackPageLoadTime();
        }}
      />
    </>
  );
}
