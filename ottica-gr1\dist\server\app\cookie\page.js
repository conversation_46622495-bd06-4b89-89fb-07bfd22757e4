(()=>{var e={};e.id=677,e.ids=[677],e.modules={273:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var s=i(7413),a=i(4536),o=i.n(a);let r=()=>{let e=new Date().getFullYear();return(0,s.jsx)("footer",{className:"bg-primary text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,s.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,s.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,s.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,s.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,s.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,s.jsx)("p",{children:"✉️ <EMAIL>"}),(0,s.jsxs)("div",{className:"pt-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,s.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,s.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,s.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,s.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,s.jsxs)("div",{className:"flex space-x-6",children:[(0,s.jsx)(o(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,s.jsx)(o(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},418:(e,t,i)=>{"use strict";function s({title:e,description:t,keywords:i="",canonical:s,ogImage:a="/images/og-default.jpg",ogType:o="website",twitterCard:r="summary_large_image",noindex:n=!1}){let l="https://otticagr1.it",c=e.includes("Ottica GR1")?e:`${e} - Ottica GR1`,d=s?`${l}${s}`:void 0,m=a.startsWith("http")?a:`${l}${a}`;return{title:c,description:t,keywords:i||void 0,robots:n?"noindex,nofollow":"index,follow",openGraph:{title:c,description:t,url:d,siteName:"Ottica GR1",images:[{url:m,width:1200,height:630,alt:e}],locale:"it_IT",type:o},twitter:{card:r,title:c,description:t,images:[m],creator:"@otticagr1",site:"@otticagr1"},alternates:{canonical:d},other:{"geo.region":"IT-RM","geo.placename":"Roma","geo.position":"41.9028;12.4964",ICBM:"41.9028, 12.4964"}}}i.d(t,{U:()=>a,Y:()=>s});let a=()=>({"@context":"https://schema.org","@type":"LocalBusiness","@id":"https://otticagr1.it/#business",name:"Ottica GR1",description:"Ottica specializzata in occhiali da vista, da sole, lenti a contatto e controllo vista a Montesacro, Roma. Dal 1982.",url:"https://otticagr1.it",telephone:"+39-**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"Via Montesacro, 123",addressLocality:"Roma",addressRegion:"Lazio",postalCode:"00141",addressCountry:"IT"},geo:{"@type":"GeoCoordinates",latitude:41.9028,longitude:12.4964},openingHoursSpecification:[{"@type":"OpeningHoursSpecification",dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"09:00",closes:"19:30"},{"@type":"OpeningHoursSpecification",dayOfWeek:"Saturday",opens:"09:00",closes:"13:00"}],priceRange:"€€",image:"https://otticagr1.it/images/og-default.jpg",logo:"https://otticagr1.it/images/logo.png",sameAs:["https://www.facebook.com/otticagr1","https://www.instagram.com/otticagr1"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Servizi Ottici",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Vista",description:"Vendita e consulenza per occhiali da vista personalizzati"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Occhiali da Sole",description:"Ampia selezione di occhiali da sole di marca"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Lenti a Contatto",description:"Lenti a contatto di tutte le tipologie"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Controllo Vista",description:"Esami della vista professionali con strumentazione avanzata"}}]}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1562:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n,metadata:()=>r});var s=i(7413),a=i(5271),o=i(273);let r=(0,i(418).Y)({title:"Cookie Policy - Ottica GR1",description:"Informativa sui cookie di Ottica GR1. Come utilizziamo i cookie per migliorare la tua esperienza sul nostro sito web.",keywords:"cookie policy, cookie, ottica gr1, informativa cookie, gdpr",canonical:"/cookie"});function n(){return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"pt-20",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold font-sans text-primary mb-8",children:"Cookie Policy"}),(0,s.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,s.jsxs)("p",{className:"text-lg text-text-base mb-8",children:["Ultimo aggiornamento: ",new Date().toLocaleDateString("it-IT")]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"1. Cosa sono i cookie"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"I cookie sono piccoli file di testo che vengono memorizzati sul tuo dispositivo quando visiti un sito web. Vengono ampiamente utilizzati per far funzionare i siti web, o per farli funzionare in modo pi\xf9 efficiente, oltre a fornire informazioni ai proprietari del sito."})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"2. Come utilizziamo i cookie"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Utilizziamo i cookie per diversi motivi:"}),(0,s.jsxs)("ul",{className:"list-disc pl-6 text-text-base mb-4",children:[(0,s.jsx)("li",{children:"Per garantire il corretto funzionamento del sito web"}),(0,s.jsx)("li",{children:"Per ricordare le tue preferenze"}),(0,s.jsx)("li",{children:"Per analizzare come viene utilizzato il nostro sito"}),(0,s.jsx)("li",{children:"Per migliorare l'esperienza utente"})]})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"3. Tipi di cookie che utilizziamo"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold font-sans text-primary mb-3",children:"Cookie essenziali"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Questi cookie sono necessari per il funzionamento del sito web e non possono essere disattivati. Sono generalmente impostati solo in risposta ad azioni da te effettuate che equivalgono a una richiesta di servizi."})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold font-sans text-primary mb-3",children:"Cookie di prestazione"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Questi cookie ci permettono di contare le visite e le fonti di traffico in modo da poter misurare e migliorare le prestazioni del nostro sito. Ci aiutano a sapere quali sono le pagine pi\xf9 e meno popolari."})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold font-sans text-primary mb-3",children:"Cookie di funzionalit\xe0"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Questi cookie consentono al sito web di fornire funzionalit\xe0 e personalizzazione migliorate. Possono essere impostati da noi o da fornitori terzi i cui servizi abbiamo aggiunto alle nostre pagine."})]})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"4. Come gestire i cookie"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Puoi controllare e/o eliminare i cookie come desideri. Puoi eliminare tutti i cookie che sono gi\xe0 sul tuo computer e puoi impostare la maggior parte dei browser per impedire che vengano inseriti."}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Tuttavia, se lo fai, potresti dover regolare manualmente alcune preferenze ogni volta che visiti un sito e alcuni servizi e funzionalit\xe0 potrebbero non funzionare."})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"5. Cookie di terze parti"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Il nostro sito web pu\xf2 utilizzare servizi di terze parti che impostano i propri cookie. Questi includono:"}),(0,s.jsxs)("ul",{className:"list-disc pl-6 text-text-base mb-4",children:[(0,s.jsx)("li",{children:"Google Analytics per l'analisi del traffico web"}),(0,s.jsx)("li",{children:"Servizi di mappe per la localizzazione"}),(0,s.jsx)("li",{children:"Widget dei social media"})]})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"6. Modifiche a questa policy"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Potremmo aggiornare questa Cookie Policy di tanto in tanto per riflettere, ad esempio, modifiche ai cookie che utilizziamo o per altri motivi operativi, legali o normativi."})]}),(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold font-sans text-primary mb-4",children:"7. Contatti"}),(0,s.jsx)("p",{className:"text-text-base mb-4",children:"Se hai domande su questa Cookie Policy, contattaci:"}),(0,s.jsxs)("div",{className:"bg-background p-6 rounded-lg border border-border-gray",children:[(0,s.jsxs)("p",{className:"text-text-base mb-2",children:[(0,s.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,s.jsxs)("p",{className:"text-text-base mb-2",children:[(0,s.jsx)("strong",{children:"Telefono:"})," 06 123 456 789"]}),(0,s.jsxs)("p",{className:"text-text-base",children:[(0,s.jsx)("strong",{children:"Indirizzo:"})," Via Montesacro, Roma"]})]})]})]})]})})}),(0,s.jsx)(o.A,{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4047:(e,t,i)=>{Promise.resolve().then(i.bind(i,3121)),Promise.resolve().then(i.t.bind(i,5814,23))},4536:(e,t,i)=>{let{createProxy:s}=i(9844);e.exports=s("C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4727:(e,t,i)=>{Promise.resolve().then(i.bind(i,5271)),Promise.resolve().then(i.t.bind(i,4536,23))},5271:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\layout\\Header.tsx","default")},7321:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=i(5239),a=i(8088),o=i(8170),r=i.n(o),n=i(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(t,l);let c={children:["",{children:["cookie",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1562)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\cookie\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,2366)),"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\cookie\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cookie/page",pathname:"/cookie",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[447,988,926],()=>i(7321));module.exports=s})();