exports.id=926,exports.ids=[926],exports.modules={919:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(687),i=s(5814),r=s.n(i);let o=()=>{let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-primary text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-white",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]}),(0,a.jsx)("p",{className:"text-sm leading-relaxed opacity-90",children:"Dal 1982 nella zona di Montesacro, tradizione e innovazione per la tua salute visiva. Occhiali da vista, da sole, lenti a contatto e controllo vista."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Link Rapidi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/storia",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"La Nostra Storia"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/servizi",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"I Nostri Servizi"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/occhiali-sole",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Occhiali da Sole"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/lenti-contatto",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Lenti a Contatto"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Servizi"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/esami-vista",className:"text-sm opacity-90 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Esami della Vista"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Consulenza Personalizzata"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Riparazioni"})}),(0,a.jsx)("li",{children:(0,a.jsx)("span",{className:"text-sm opacity-90",children:"Assistenza Post-Vendita"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans",children:"Contatti"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm opacity-90",children:[(0,a.jsx)("p",{children:"\uD83D\uDCCD Via Conca D'Oro, 323, 00141 ROMA RM"}),(0,a.jsx)("p",{children:"\uD83D\uDCDE 06 8862962 | +39 3928480621 "}),(0,a.jsx)("p",{children:"✉️ <EMAIL>"}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Orari di Apertura:"}),(0,a.jsx)("p",{children:"Lun: 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Mar-Sab: 9:30 - 13:00 , 16:00 - 19:30"}),(0,a.jsx)("p",{children:"Dom: Chiuso"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-primary-light mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",e," Ottica GR1 SRL. P.IVA 05194191002 | Powered by Programmarti.it"]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(r(),{href:"/privacy",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Privacy Policy"}),(0,a.jsx)(r(),{href:"/cookie",className:"text-sm opacity-75 hover:opacity-100 hover:text-accent transition-colors duration-300",children:"Cookie Policy"})]})]})})]})})}},982:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\ui\\WhatsAppButton.tsx","default")},1683:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\analytics\\\\PageTracker.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\analytics\\PageTracker.tsx","default")},2294:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(687);let i=({children:e,variant:t="primary",size:s="md",fullWidth:i=!1,className:r="",...o})=>{let n=`font-sans font-medium transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${({primary:"bg-primary text-white hover:bg-primary-dark focus:ring-primary",secondary:"bg-gray-200 text-text-base hover:bg-gray-300 focus:ring-gray-400",accent:"bg-accent text-white hover:bg-accent-light focus:ring-accent",outline:"border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary"})[t]} ${({sm:"px-3 py-1.5 text-sm",md:"px-6 py-2.5 text-base",lg:"px-8 py-3 text-lg"})[s]} ${i?"w-full":""} ${r}`.trim();return(0,a.jsx)("button",{className:n,...o,children:e})}},2366:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\app\\not-found.tsx","default")},2704:()=>{},3109:(e,t,s)=>{Promise.resolve().then(s.bind(s,7140))},3121:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(687),i=s(3210),r=s(5814),o=s.n(r),n=s(474),l=s(6189);let c=()=>{let[e,t]=(0,i.useState)(!1),[s,r]=(0,i.useState)(!1),c=(0,l.usePathname)();(0,i.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,i.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]);let d=[{href:"/",label:"HOME"},{href:"/storia",label:"STORIA"},{href:"/servizi",label:"SERVIZI"},{href:"/occhiali",label:"OCCHIALI DA VISTA"},{href:"/lenti-contatto",label:"LENTI A CONTATTO"},{href:"/occhiali-sole",label:"OCCHIALI DA SOLE"},{href:"/esami-vista",label:"ESAMI DELLA VISTA"},{href:"/contatti",label:"CONTATTI"}];return(0,a.jsx)("header",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${e?"bg-white shadow-lg":"/"===c||s?"bg-white shadow-sm":"bg-white/90 shadow-sm"}`,children:(0,a.jsxs)("nav",{className:"container mx-auto px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(o(),{href:"/",className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"relative h-10 w-10 flex-shrink-0",children:(0,a.jsx)(n.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain",priority:!0})}),(0,a.jsxs)("div",{className:"text-2xl font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-primary transition-colors duration-300",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent transition-colors duration-300 ml-1",children:"GR1"})]})]}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:d.map(e=>(0,a.jsx)(o(),{href:e.href,className:`font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ${c===e.href?"text-primary":"text-text-base hover:text-primary"}`,children:e.label},e.href))}),(0,a.jsxs)("button",{className:"lg:hidden flex flex-col space-y-1 w-6 h-6",onClick:()=>r(!s),"aria-label":"Toggle mobile menu",children:[(0,a.jsx)("span",{className:`block h-0.5 w-6 transition-all duration-300 bg-primary ${s?"rotate-45 translate-y-1.5":""}`}),(0,a.jsx)("span",{className:`block h-0.5 w-6 transition-all duration-300 bg-primary ${s?"opacity-0":""}`}),(0,a.jsx)("span",{className:`block h-0.5 w-6 transition-all duration-300 bg-primary ${s?"-rotate-45 -translate-y-1.5":""}`})]})]}),s&&(0,a.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]",onClick:()=>r(!1)}),(0,a.jsx)("div",{className:`lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"translate-x-full"}`,style:{backgroundColor:"#ffffff",backdropFilter:"none"},children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"relative h-8 w-8 flex-shrink-0",children:(0,a.jsx)(n.default,{src:"/images/logo/logo3.png",alt:"Ottica GR1 Logo",fill:!0,className:"object-contain"})}),(0,a.jsxs)("div",{className:"text-lg font-bold font-sans",children:[(0,a.jsx)("span",{className:"text-primary",children:"OTTICA"}),(0,a.jsx)("span",{className:"text-accent ml-1",children:"GR1"})]})]}),(0,a.jsx)("button",{onClick:()=>r(!1),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Close menu",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsx)("nav",{className:"flex-1 px-6 py-6",children:(0,a.jsx)("div",{className:"space-y-2",children:d.map(e=>(0,a.jsx)(o(),{href:e.href,className:`block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ${c===e.href?"text-primary bg-primary/10 border-l-4 border-primary":"text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1"}`,onClick:()=>r(!1),children:e.label},e.href))})}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Dal 1982 a Montesacro"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Tradizione e innovazione"})]})})]})})]})})}},3975:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(3210),i=s(6189),r=s(5036);let o=()=>{let e=(0,i.usePathname)(),t=(0,i.useSearchParams)();(0,a.useEffect)(()=>{if(e){let s=e+(t?.toString()?`?${t.toString()}`:"");(0,r.trackPageView)(s)}},[e,t])},n=()=>{(0,a.useEffect)(()=>{let e=Date.now(),t=!0,s=()=>{document.hidden?t=!1:(t=!0,e=Date.now())},a=()=>{if(t){let t=Date.now()-e;t>1e4&&navigator.sendBeacon("/api/analytics/engagement",JSON.stringify({time:t,page:window.location.pathname}))}};return document.addEventListener("visibilitychange",s),window.addEventListener("beforeunload",a),()=>{document.removeEventListener("visibilitychange",s),window.removeEventListener("beforeunload",a)}},[])};function l(){return o(),n(),null}},4648:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(687),i=s(3210),r=s(2294);let o=()=>{let[e,t]=(0,i.useState)(!1),[o,n]=(0,i.useState)(!1),[l,c]=(0,i.useState)({necessary:!0,analytics:!1,marketing:!1});(0,i.useEffect)(()=>{let e=localStorage.getItem("ottica-gr1-cookie-consent");if(e){let t=JSON.parse(e);c(t),d(t)}else t(!0)},[]);let d=e=>{Promise.resolve().then(s.bind(s,5036)).then(({updateConsent:t})=>{t(e.analytics,e.marketing)})},m=e=>{localStorage.setItem("ottica-gr1-cookie-consent",JSON.stringify(e)),c(e),t(!1),d(e)},x=e=>{"necessary"!==e&&c(t=>({...t,[e]:!t[e]}))};return e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-border-gray shadow-lg",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold font-sans text-text-base mb-2",children:"Utilizziamo i Cookie"}),(0,a.jsx)("p",{className:"text-sm text-text-base opacity-80 leading-relaxed",children:"Utilizziamo cookie necessari per il funzionamento del sito e cookie opzionali per analisi e marketing. Puoi scegliere quali accettare o rifiutare tutti i cookie opzionali."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 min-w-fit",children:[(0,a.jsx)(r.A,{variant:"outline",size:"sm",onClick:()=>n(!0),className:"text-sm",children:"Personalizza"}),(0,a.jsx)(r.A,{variant:"outline",size:"sm",onClick:()=>{m({necessary:!0,analytics:!1,marketing:!1})},className:"text-sm",children:"Rifiuta Tutto"}),(0,a.jsx)(r.A,{variant:"primary",size:"sm",onClick:()=>{m({necessary:!0,analytics:!0,marketing:!0})},className:"text-sm",children:"Accetta Tutto"})]})]})})}),o&&(0,a.jsx)("div",{className:"fixed inset-0 z-60 bg-black bg-opacity-50 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold font-sans text-text-base mb-4",children:"Preferenze Cookie"}),(0,a.jsx)("p",{className:"text-text-base opacity-80 mb-6",children:"Gestisci le tue preferenze sui cookie. I cookie necessari sono sempre attivi per garantire il corretto funzionamento del sito."}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border border-border-gray rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold font-sans text-text-base",children:"Cookie Necessari"}),(0,a.jsx)("div",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium",children:"Sempre Attivi"})]}),(0,a.jsx)("p",{className:"text-sm text-text-base opacity-80",children:"Questi cookie sono essenziali per il funzionamento del sito web e non possono essere disabilitati."})]}),(0,a.jsxs)("div",{className:"border border-border-gray rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold font-sans text-text-base",children:"Cookie Analitici"}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.analytics,onChange:()=>x("analytics"),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"})]})]}),(0,a.jsx)("p",{className:"text-sm text-text-base opacity-80",children:"Ci aiutano a capire come i visitatori interagiscono con il sito raccogliendo informazioni anonime."})]}),(0,a.jsxs)("div",{className:"border border-border-gray rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold font-sans text-text-base",children:"Cookie Marketing"}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:l.marketing,onChange:()=>x("marketing"),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"})]})]}),(0,a.jsx)("p",{className:"text-sm text-text-base opacity-80",children:"Utilizzati per mostrare annunci pubblicitari pi\xf9 rilevanti per te e i tuoi interessi."})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-8",children:[(0,a.jsx)(r.A,{variant:"outline",onClick:()=>n(!1),className:"flex-1",children:"Annulla"}),(0,a.jsx)(r.A,{variant:"primary",onClick:()=>{m(l),n(!1)},className:"flex-1",children:"Salva Preferenze"})]})]})})})]}):null}},5036:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GA_MEASUREMENT_ID:()=>a,initClickTracking:()=>N,initGA:()=>i,initScrollTracking:()=>j,trackButtonClick:()=>l,trackConversion:()=>y,trackEmailClick:()=>g,trackEngagement:()=>b,trackEvent:()=>n,trackFileDownload:()=>m,trackFormSubmission:()=>d,trackLinkClick:()=>c,trackPageLoadTime:()=>k,trackPageView:()=>o,trackPhoneCall:()=>f,trackPurchase:()=>v,trackScrollDepth:()=>h,trackSearch:()=>p,trackVideoInteraction:()=>x,trackWhatsAppClick:()=>u,updateConsent:()=>r});let a=process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID||"",i=()=>{},r=(e,t)=>{},o=(e,t)=>{},n=(e,t,s,a,i)=>{},l=(e,t)=>{n("click","button",`${e} - ${t}`)},c=(e,t,s)=>{n("click","link",`${e} - ${s}`,void 0,{link_url:t})},d=(e,t)=>{n("form_submit","form",e,void 0,{success:t})},m=(e,t)=>{n("file_download","download",e,void 0,{file_type:t})},x=(e,t,s)=>{n(e,"video",t,s)},h=e=>{n("scroll","engagement",`${e}%`,e)},p=(e,t)=>{n("search","engagement",e,t)},f=(e,t)=>{n("phone_call","contact",`${e} - ${t}`)},u=e=>{n("whatsapp_click","contact",e)},g=e=>{n("email_click","contact",e)},v=(e,t,s="EUR")=>{n("purchase","ecommerce",e,t,{transaction_id:e,currency:s})},b=e=>{n("user_engagement","engagement","time_on_page",e)},y=(e,t)=>{n("conversion","goal",e,t)},j=()=>{},N=()=>{},k=()=>{}},5243:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},6101:(e,t,s)=>{Promise.resolve().then(s.bind(s,9995)),Promise.resolve().then(s.bind(s,1683)),Promise.resolve().then(s.bind(s,9590)),Promise.resolve().then(s.bind(s,982)),Promise.resolve().then(s.t.bind(s,7429,23))},6661:(e,t,s)=>{Promise.resolve().then(s.bind(s,2366))},7140:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(687),i=s(3121),r=s(919),o=s(2294);function n(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsx)("section",{className:"py-16 bg-gradient-to-b from-primary/5 to-background min-h-[80vh] flex items-center",children:(0,a.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsx)("div",{className:"text-8xl font-bold text-primary mb-8",children:"404"}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold font-sans text-text-base mb-6",children:"Pagina Non Trovata"}),(0,a.jsx)("p",{className:"text-xl text-text-base opacity-80 mb-8 leading-relaxed",children:"Ci dispiace, la pagina che stai cercando non esiste o \xe8 stata spostata."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(o.A,{variant:"primary",size:"lg",onClick:()=>window.location.href="/",children:"Torna alla Homepage"}),(0,a.jsx)(o.A,{variant:"outline",size:"lg",onClick:()=>window.location.href="/contatti",children:"Contattaci"})]})]})})})}),(0,a.jsx)(r.A,{})]})}},7989:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(687);s(3210);var i=s(2600),r=s(5036);function o({measurementId:e}){return e?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i.default,{src:`https://www.googletagmanager.com/gtag/js?id=${e}`,strategy:"afterInteractive",onLoad:()=>{(0,r.initGA)(),(0,r.initScrollTracking)(),(0,r.initClickTracking)(),(0,r.trackPageLoadTime)()}})}):null}},8014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>m});var a=s(7413),i=s(4415),r=s.n(i),o=s(6162);s(2704);var n=s(9590),l=s(982),c=s(9995),d=s(1683);let m={title:"Ottica GR1 - Occhiali da Vista, da Sole e Lenti a Contatto",description:"Ottica GR1 a Montesacro dal 1982. Occhiali da vista, da sole, lenti a contatto e controllo vista. Tradizione, qualit\xe0 e innovazione per la tua salute visiva.",keywords:"ottica, occhiali da vista, occhiali da sole, lenti a contatto, controllo vista, Montesacro, Roma",icons:{icon:[{url:"/favicon-16x16.png",sizes:"16x16",type:"image/png"},{url:"/favicon-32x32.png",sizes:"32x32",type:"image/png"},{url:"/favicon.ico",sizes:"any"}],apple:[{url:"/apple-touch-icon.png",sizes:"180x180",type:"image/png"}],other:[{rel:"android-chrome-192x192",url:"/android-chrome-192x192.png"},{rel:"android-chrome-512x512",url:"/android-chrome-512x512.png"}]},manifest:"/site.webmanifest"};function x({children:e}){return(0,a.jsxs)("html",{lang:"it",className:r().variable,children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,a.jsxs)("body",{className:"font-serif antialiased",children:[e,(0,a.jsx)(n.default,{}),(0,a.jsx)(l.default,{}),(0,a.jsx)(d.default,{}),(0,a.jsx)(c.default,{measurementId:process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID||""}),(0,a.jsx)(o.default,{src:"https://cdn.trustindex.io/loader.js",strategy:"afterInteractive"})]})]})}},8291:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},9253:(e,t,s)=>{Promise.resolve().then(s.bind(s,7989)),Promise.resolve().then(s.bind(s,3975)),Promise.resolve().then(s.bind(s,4648)),Promise.resolve().then(s.bind(s,9748)),Promise.resolve().then(s.t.bind(s,9167,23))},9590:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\ui\\\\CookieConsent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\ui\\CookieConsent.tsx","default")},9748:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(687),i=s(3210),r=s(5036);let o=()=>{let[e,t]=(0,i.useState)(!1);return(0,a.jsx)("div",{className:"fixed bottom-24 right-6 z-50",children:(0,a.jsxs)("button",{onClick:()=>{(0,r.trackWhatsAppClick)("floating_button");let e=encodeURIComponent("Buongiorno vorrei prenotare un appuntamento"),t=`https://wa.me/+393928480621?text=${e}`;window.open(t,"_blank")},onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),className:"group relative bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110","aria-label":"Contattaci su WhatsApp",children:[(0,a.jsx)("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"currentColor",className:"transition-transform duration-300",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"})}),e&&(0,a.jsxs)("div",{className:"absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:["Scrivici su WhatsApp",(0,a.jsx)("div",{className:"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"})]}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20"})]})})}},9995:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progettini\\\\ottica-gr1\\\\ottica-gr1\\\\components\\\\analytics\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Progettini\\ottica-gr1\\ottica-gr1\\components\\analytics\\GoogleAnalytics.tsx","default")}};