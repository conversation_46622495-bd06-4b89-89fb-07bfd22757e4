import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import EsamiVistaContent from './EsamiVistaContent';
import { generateSEOMetadata } from '../../components/seo/SEOHead';

export const metadata = generateSEOMetadata({
  title: 'Esami della Vista - Controllo Professionale con Tecnologie Avanzate',
  description: 'Esami della vista professionali a Montesacro. Autorefractometro, topografo corneale, forottero computerizzato. Prenotazione controllo vista gratuito.',
  keywords: 'esami della vista roma, controllo vista montesacro, autorefractometro, topografo corneale, optometrista, misurazione vista, prenotazione',
  canonical: '/esami-vista',
});

export default function EsamiVistaPage() {
  return (
    <div className="min-h-screen">
      <Header />

      <main className="pt-20">
        <EsamiVistaContent />
      </main>

      <Footer />
    </div>
  );
}
