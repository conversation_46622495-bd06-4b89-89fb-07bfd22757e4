'use client';

import { useState } from 'react';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { trackFormSubmission, trackButtonClick, trackPhoneCall } from '../../lib/analytics';

export default function ContattiContent() {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefono: '',
    servizio: '',
    messaggio: '',
    privacyConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const target = e.target;
    const { name, value, type } = target;
    const checked = 'checked' in target ? target.checked : false;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    // Validate privacy consent
    if (!formData.privacyConsent) {
      setSubmitStatus('error');
      setErrorMessage('È necessario accettare il trattamento dei dati personali per inviare il messaggio.');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        // Track successful form submission
        trackFormSubmission('contact_form', true);
        setFormData({
          nome: '',
          email: '',
          telefono: '',
          servizio: '',
          messaggio: '',
          privacyConsent: false
        });
      } else {
        setSubmitStatus('error');
        // Track failed form submission
        trackFormSubmission('contact_form', false);
        setErrorMessage(result.error || 'Si è verificato un errore durante l\'invio del messaggio.');
      }
    } catch (error) {
      setSubmitStatus('error');
      // Track form submission error
      trackFormSubmission('contact_form', false);
      setErrorMessage('Errore di connessione. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: '📍',
      title: 'Indirizzo',
      details: ['Via Conca D Oro, 323', '00141 Roma (RM)'],
      action: 'Vedi su Maps'
    },
    {
      icon: '📞',
      title: 'Telefono',
      details: ['06 8862962' , '+39 3928480621  '],
      action: 'Chiama ora'
    },
    {
      icon: '🅿️',
      title: 'Parcheggio Convenzionato',
      details: ['Via Val Maira 6', '00141 Roma (RM)'],
      action: 'Vedi su Maps'
    },
    {
      icon: '🕒',
      title: 'Orari di Apertura',
      details: ['Lun: 16:00 - 19:30', 'Mar - Sab: 9:00 - 13:00' , '16:30 - 19:30', 'Dom: Chiuso'],
      action: null
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
              Contattaci
            </h1>
            <p className="text-xl md:text-2xl text-text-base opacity-80 leading-relaxed">
              Siamo qui per aiutarti. Vieni a trovarci o contattaci per qualsiasi informazione.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <Card key={index} className="text-center h-full">
                <div className="p-6">
                  <div className="text-4xl mb-4">{info.icon}</div>
                  <h3 className="text-xl font-semibold font-sans text-text-base mb-4">
                    {info.title}
                  </h3>
                  <div className="space-y-1 mb-4">
                    {info.details.map((detail, idx) => (
                      <p key={idx} className="text-text-base opacity-80">
                        {detail}
                      </p>
                    ))}
                  </div>
                  {info.action && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (info.title === 'Telefono') {
                          window.location.href = 'tel:068862962';
                        } else if (info.title === 'Parcheggio Convenzionato') {
                          window.open('https://maps.google.com/?q=Via+Val+Maira+6+Roma', '_blank');
                        } else if (info.title === 'Indirizzo') {
                          window.open('https://maps.google.com/?q=Via+Conca+d+Oro+323+Roma', '_blank');
                        }
                      }}
                    >
                      {info.action}
                    </Button>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Form */}
            <div>
              <h2 className="text-3xl font-bold font-sans text-text-base mb-6">
                Inviaci un Messaggio
              </h2>
              <p className="text-lg text-text-base opacity-80 mb-8">
                Compila il form e ti contatteremo il prima possibile per rispondere 
                alle tue domande o prenotare un appuntamento.
              </p>
              
              {/* Status Messages */}
              {submitStatus === 'success' && (
                <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                  <div className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Grazie per il tuo messaggio! Ti contatteremo presto.</span>
                  </div>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                  <div className="flex items-center">
                    <span className="text-red-500 mr-2">✗</span>
                    <span>{errorMessage}</span>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="nome" className="block text-sm font-medium text-text-base mb-2">
                      Nome *
                    </label>
                    <input
                      type="text"
                      id="nome"
                      name="nome"
                      value={formData.nome}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Il tuo nome"
                    />
                  </div>
                  <div>
                    <label htmlFor="telefono" className="block text-sm font-medium text-text-base mb-2">
                      Telefono *
                    </label>
                    <input
                      type="tel"
                      id="telefono"
                      name="telefono"
                      value={formData.telefono}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Il tuo numero"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-text-base mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label htmlFor="servizio" className="block text-sm font-medium text-text-base mb-2">
                    Servizio di Interesse
                  </label>
                  <select
                    id="servizio"
                    name="servizio"
                    value={formData.servizio}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Seleziona un servizio</option>
                    <option value="controllo-vista">Controllo Vista</option>
                    <option value="occhiali-vista">Occhiali da Vista</option>
                    <option value="occhiali-sole">Occhiali da Sole</option>
                    <option value="lenti-contatto">Lenti a Contatto</option>
                    <option value="riparazione">Riparazione</option>
                    <option value="altro">Altro</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="messaggio" className="block text-sm font-medium text-text-base mb-2">
                    Messaggio *
                  </label>
                  <textarea
                    id="messaggio"
                    name="messaggio"
                    value={formData.messaggio}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 border border-border-gray rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical"
                    placeholder="Scrivi qui il tuo messaggio..."
                  />
                </div>

                {/* Privacy Consent Checkbox */}
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="privacyConsent"
                    name="privacyConsent"
                    checked={formData.privacyConsent}
                    onChange={handleInputChange}
                    required
                    className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border-gray rounded"
                  />
                  <label htmlFor="privacyConsent" className="text-sm text-text-base leading-relaxed">
                    Accetto il trattamento dei dati personali secondo la{' '}
                    <a
                      href="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary-dark underline font-medium"
                    >
                      Privacy Policy
                    </a>
                    {' '}*
                  </label>
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  fullWidth
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Invio in corso...' : 'Invia Messaggio'}
                </Button>
              </form>
            </div>

            {/* Map and Info */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-bold font-sans text-text-base mb-4">
                  Dove Siamo
                </h3>
                <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2967.611162286211!2d12.5182373!3d41.944206099999995!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x132f66b2ed4f3b67%3A0xdbd9c055e4803223!2sOttica%20G.R.1%20S.r.l.!5e0!3m2!1sit!2sit!4v1752164767506!5m2!1sit!2sit" width="800" height="300" loading="lazy"></iframe>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold font-sans text-text-base mb-4">
                  Come Raggiungerci
                </h3>
                <div className="space-y-3 text-text-base opacity-80">
                  <div className="flex items-start">
                    <span className="text-accent mr-2 mt-1">🚇</span>
                    <div>
                      <strong>Metro:</strong> Linea B1 - Fermata Conca d'Oro
                    </div>
                  </div>
                  <div className="flex items-start">
                    <span className="text-accent mr-2 mt-1">🚌</span>
                    <div>
                      <strong>Autobus:</strong> Linee 38, 86, 90, 135
                    </div>
                  </div>
                  <div className="flex items-start">
                    <span className="text-accent mr-2 mt-1">🚗</span>
                    <div>
                      <strong>Auto:</strong> Parcheggio convenzionato
                    </div>
                  </div>

                  <div className="flex items-start">
                    <span className="text-accent mr-2 mt-1">🅿️</span>
                    <div>
                      <strong>Parcheggio Convenzionato:</strong>  Roma Prime Garage -  Via Val Maira 6
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
            Vieni a Trovarci
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Il nostro team di esperti ti aspetta per offrirti il miglior servizio 
            per la tua salute visiva.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="accent"
              size="lg"
              onClick={() => {
                trackButtonClick('Prenota Controllo Vista', 'contatti_cta');
                window.location.href = '/esami-vista';
              }}
            >
              Prenota Controllo Vista
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => {
                trackPhoneCall('068862962', 'contatti_cta');
                window.location.href = 'tel:068862962';
              }}
              className="border-white text-white hover:bg-white hover:text-primary"
            >
              Chiama: 068862962
            </Button>
          </div>
        </div>
      </section>
    </>
  );
}
