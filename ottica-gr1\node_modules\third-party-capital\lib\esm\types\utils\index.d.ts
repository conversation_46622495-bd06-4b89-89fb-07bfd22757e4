import type { Data, Inputs, HtmlAttributes } from '../types';
export declare function formatUrl(url: string, params?: string[], args?: Inputs, slug?: Inputs): string;
export declare function createHtml(element: string, attributes?: HtmlAttributes, htmlAttrArgs?: Inputs, urlQueryParamArgs?: Inputs, slugParamArg?: Inputs): string;
export declare function formatData(data: Data, args: Inputs): {
    html: string | null;
    scripts: {
        url: string;
        params?: string[] | undefined;
        strategy: "server" | "client" | "idle" | "worker";
        location: "head" | "body";
        action: "append" | "prepend";
    }[] | null;
    id: string;
    description: string;
    website?: string | undefined;
    stylesheets?: string[] | undefined;
};
//# sourceMappingURL=index.d.ts.map